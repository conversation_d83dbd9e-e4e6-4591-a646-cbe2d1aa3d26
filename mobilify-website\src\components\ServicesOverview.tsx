import React from 'react';
import AnimatedSection from './AnimatedSection';
import StaggeredList from './StaggeredList';

const services = [
  {
    title: 'Starter App',
    price: '$5,000',
    description: 'Perfect for converting existing websites into mobile apps',
    features: [
      'Website to app conversion',
      'Basic customization',
      'App store submission',
      '30 days support'
    ],
    popular: false
  },
  {
    title: 'Custom App',
    price: '$15,000',
    description: 'Turn your innovative ideas into reality with custom development',
    features: [
      'Custom app development',
      'Advanced features',
      'UI/UX design included',
      'App store optimization',
      '90 days support'
    ],
    popular: true
  },
  {
    title: 'Enterprise',
    price: 'Contact Us',
    description: 'Bespoke solutions for complex projects and large organizations',
    features: [
      'Complex integrations',
      'Scalable architecture',
      'Dedicated team',
      'Ongoing maintenance',
      'Priority support'
    ],
    popular: false
  }
];

export default function ServicesOverview() {
  return (
    <section id="services-overview" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <AnimatedSection className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold text-foreground mb-6">
            Solutions Tailored to <span className="text-primary">Your Needs</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Whether you're converting an existing website or building from scratch,
            we have the perfect package for your project.
          </p>
        </AnimatedSection>

        {/* Service Cards */}
        <StaggeredList className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {services.map((service, index) => (
            <div
              key={index}
              className={`relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:scale-105 group cursor-pointer ${
                service.popular ? 'ring-2 ring-primary scale-105' : ''
              }`}
            >
              {service.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-primary text-white px-4 py-2 rounded-full text-sm font-semibold">
                    Most Popular
                  </span>
                </div>
              )}
              
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-foreground mb-2">{service.title}</h3>
                <div className="text-3xl font-bold text-primary mb-4">{service.price}</div>
                <p className="text-gray-600">{service.description}</p>
              </div>

              <ul className="space-y-3 mb-8">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center">
                    <svg className="w-5 h-5 text-primary mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              <button className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 transform group-hover:scale-105 ${
                service.popular
                  ? 'bg-primary text-white hover:bg-primary/90 shadow-lg hover:shadow-xl'
                  : 'bg-gray-100 text-gray-800 hover:bg-primary hover:text-white shadow-md hover:shadow-lg'
              }`}>
                Get Started
              </button>
            </div>
          ))}
        </StaggeredList>

        {/* Deep Dive Link */}
        <AnimatedSection className="text-center" delay={0.6}>
          <button className="inline-flex items-center bg-white text-primary px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-50 transition-all duration-200 shadow-md hover:shadow-lg">
            Compare All Features & Pricing
            <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </button>
        </AnimatedSection>
      </div>
    </section>
  );
}
