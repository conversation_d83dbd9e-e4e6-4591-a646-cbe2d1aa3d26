import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AnimatedSection from '@/components/AnimatedSection';
import StaggeredList from '@/components/StaggeredList';

const teamMembers = [
  {
    name: '<PERSON>',
    role: 'CEO & Visionary',
    bio: 'Former Product Lead at Shopify with 8+ years of experience in mobile app development. <PERSON> is passionate about helping small businesses succeed and frustrated with the high barriers to mobile app development. He founded Mobilify to democratize app creation.',
    expertise: ['Product Strategy', 'Business Development', 'User Experience', 'Team Leadership'],
    image: '/team/alex-chen.jpg' // Placeholder - would use AI-generated image
  },
  {
    name: '<PERSON>',
    role: 'CTO & Lead Architect',
    bio: 'Former Senior Staff Engineer at Twilio with expertise in scalable systems and AI. <PERSON> has built developer tools used by millions and brings her vision of AI-democratized development to Mobilify. She leads our technical architecture and innovation.',
    expertise: ['System Architecture', 'AI/ML', 'Mobile Development', 'DevOps'],
    image: '/team/maria-garcia.jpg' // Placeholder - would use AI-generated image
  }
];

const values = [
  {
    title: 'Quality Craftsmanship',
    description: 'Every line of code is written with precision and care. We believe in building apps that not only work flawlessly but also delight users.',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    )
  },
  {
    title: 'Client Partnership',
    description: 'We work alongside you as true partners in success. Your goals become our goals, and we\'re not satisfied until you achieve them.',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
    )
  },
  {
    title: 'Transparency',
    description: 'Clear communication and honest timelines, always. We believe in keeping you informed every step of the way.',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
      </svg>
    )
  },
  {
    title: 'Innovation',
    description: 'We stay at the forefront of technology to bring you the latest and most effective solutions for your mobile app needs.',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
      </svg>
    )
  }
];

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="pt-16">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <AnimatedSection className="text-center">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight mb-6">
                About <span className="text-primary">Mobilify</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                We're more than just developers. We're passionate about helping founders, 
                entrepreneurs, and businesses bridge the gap between vision and execution.
              </p>
            </AnimatedSection>
          </div>
        </section>

        {/* Mission Section */}
        <section className="py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <AnimatedSection className="bg-white rounded-3xl p-8 md:p-12 shadow-xl">
              <h2 className="text-3xl font-bold text-foreground mb-8 text-center">Our Mission</h2>
              <div className="space-y-6 text-lg text-gray-700 leading-relaxed">
                <p>
                  At Mobilify, we believe that every great idea deserves to become a reality. 
                  We're passionate about helping founders, entrepreneurs, and businesses bridge 
                  the gap between vision and execution.
                </p>
                <p>
                  Our commitment goes beyond just writing code. We partner with you to understand 
                  your goals, challenges, and dreams. We're not satisfied until your app not only 
                  works flawlessly but also delights your users and drives your business forward.
                </p>
                <p>
                  Founded in 2024, Mobilify was born from the frustration of seeing great ideas 
                  never make it to market due to technical barriers and high development costs. 
                  We set out to change that by making professional mobile app development 
                  accessible to everyone.
                </p>
              </div>
            </AnimatedSection>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <AnimatedSection className="text-center mb-16">
              <h2 className="text-4xl font-bold text-foreground mb-6">
                Our <span className="text-primary">Values</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                These principles guide everything we do and shape how we work with our clients.
              </p>
            </AnimatedSection>

            <StaggeredList className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {values.map((value, index) => (
                <div key={index} className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-6 group-hover:bg-primary/20 transition-colors duration-300">
                    <div className="text-primary">
                      {value.icon}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-4">{value.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{value.description}</p>
                </div>
              ))}
            </StaggeredList>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <AnimatedSection className="text-center mb-16">
              <h2 className="text-4xl font-bold text-foreground mb-6">
                Meet Our <span className="text-primary">Team</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                The passionate individuals behind Mobilify who are dedicated to bringing your ideas to life.
              </p>
            </AnimatedSection>

            <StaggeredList className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {teamMembers.map((member, index) => (
                <div key={index} className="bg-white rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300">
                  <div className="flex flex-col items-center text-center">
                    {/* Placeholder for team member photo */}
                    <div className="w-32 h-32 bg-gray-200 rounded-full mb-6 flex items-center justify-center">
                      <span className="text-4xl font-bold text-gray-400">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    
                    <h3 className="text-2xl font-bold text-foreground mb-2">{member.name}</h3>
                    <p className="text-primary font-semibold mb-4">{member.role}</p>
                    <p className="text-gray-600 leading-relaxed mb-6">{member.bio}</p>
                    
                    <div className="w-full">
                      <h4 className="font-semibold text-foreground mb-3">Expertise:</h4>
                      <div className="flex flex-wrap gap-2 justify-center">
                        {member.expertise.map((skill, skillIndex) => (
                          <span
                            key={skillIndex}
                            className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </StaggeredList>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AnimatedSection>
              <h2 className="text-4xl font-bold text-foreground mb-6">
                Ready to Work Together?
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Let's discuss your project and see how we can help bring your vision to life.
              </p>
              <a
                href="/#contact"
                className="bg-primary text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary/90 transition-all duration-300 transform hover:scale-105 inline-block"
              >
                Get in Touch
              </a>
            </AnimatedSection>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
