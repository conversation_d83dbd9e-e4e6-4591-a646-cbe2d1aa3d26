'use client';

import React from 'react';

interface AnimatedSectionProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}

export default function AnimatedSection({
  children,
  className = '',
  delay = 0,
  direction = 'up'
}: AnimatedSectionProps) {
  // Temporary: Return a simple div without animations
  // TODO: Add framer-motion animations later
  return (
    <div className={className}>
      {children}
    </div>
  );
}
