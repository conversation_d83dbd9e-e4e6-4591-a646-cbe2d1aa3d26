{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/InteractiveDemo.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/InteractiveDemo.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/InteractiveDemo.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/InteractiveDemo.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/InteractiveDemo.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/InteractiveDemo.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/AnimatedSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AnimatedSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AnimatedSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/AnimatedSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AnimatedSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AnimatedSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/StaggeredList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StaggeredList.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StaggeredList.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/StaggeredList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StaggeredList.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StaggeredList.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ServicesOverview.tsx"], "sourcesContent": ["import React from 'react';\nimport AnimatedSection from './AnimatedSection';\nimport StaggeredList from './StaggeredList';\n\nconst services = [\n  {\n    title: 'Starter App',\n    price: '$5,000',\n    description: 'Perfect for converting existing websites into mobile apps',\n    features: [\n      'Website to app conversion',\n      'Basic customization',\n      'App store submission',\n      '30 days support'\n    ],\n    popular: false\n  },\n  {\n    title: 'Custom App',\n    price: '$15,000',\n    description: 'Turn your innovative ideas into reality with custom development',\n    features: [\n      'Custom app development',\n      'Advanced features',\n      'UI/UX design included',\n      'App store optimization',\n      '90 days support'\n    ],\n    popular: true\n  },\n  {\n    title: 'Enterprise',\n    price: 'Contact Us',\n    description: 'Bespoke solutions for complex projects and large organizations',\n    features: [\n      'Complex integrations',\n      'Scalable architecture',\n      'Dedicated team',\n      'Ongoing maintenance',\n      'Priority support'\n    ],\n    popular: false\n  }\n];\n\nexport default function ServicesOverview() {\n  return (\n    <section id=\"services-overview\" className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <AnimatedSection className=\"text-center mb-16\">\n          <h2 className=\"text-4xl sm:text-5xl font-bold text-foreground mb-6\">\n            Solutions Tailored to <span className=\"text-primary\">Your Needs</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Whether you're converting an existing website or building from scratch,\n            we have the perfect package for your project.\n          </p>\n        </AnimatedSection>\n\n        {/* Service Cards */}\n        <StaggeredList className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n          {services.map((service, index) => (\n            <div\n              key={index}\n              className={`relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:scale-105 group cursor-pointer ${\n                service.popular ? 'ring-2 ring-primary scale-105' : ''\n              }`}\n            >\n              {service.popular && (\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                  <span className=\"bg-primary text-white px-4 py-2 rounded-full text-sm font-semibold\">\n                    Most Popular\n                  </span>\n                </div>\n              )}\n              \n              <div className=\"text-center mb-6\">\n                <h3 className=\"text-2xl font-bold text-foreground mb-2\">{service.title}</h3>\n                <div className=\"text-3xl font-bold text-primary mb-4\">{service.price}</div>\n                <p className=\"text-gray-600\">{service.description}</p>\n              </div>\n\n              <ul className=\"space-y-3 mb-8\">\n                {service.features.map((feature, featureIndex) => (\n                  <li key={featureIndex} className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-primary mr-3 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span className=\"text-gray-700\">{feature}</span>\n                  </li>\n                ))}\n              </ul>\n\n              <button className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 transform group-hover:scale-105 ${\n                service.popular\n                  ? 'bg-primary text-white hover:bg-primary/90 shadow-lg hover:shadow-xl'\n                  : 'bg-gray-100 text-gray-800 hover:bg-primary hover:text-white shadow-md hover:shadow-lg'\n              }`}>\n                Get Started\n              </button>\n            </div>\n          ))}\n        </StaggeredList>\n\n        {/* Deep Dive Link */}\n        <AnimatedSection className=\"text-center\" delay={0.6}>\n          <button className=\"inline-flex items-center bg-white text-primary px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-50 transition-all duration-200 shadow-md hover:shadow-lg\">\n            Compare All Features & Pricing\n            <svg className=\"ml-2 w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n            </svg>\n          </button>\n        </AnimatedSection>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,WAAW;IACf;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAoB,WAAU;kBACxC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,qIAAA,CAAA,UAAe;oBAAC,WAAU;;sCACzB,8OAAC;4BAAG,WAAU;;gCAAsD;8CAC5C,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAEvD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC,mIAAA,CAAA,UAAa;oBAAC,WAAU;8BACtB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAEC,WAAW,CAAC,6JAA6J,EACvK,QAAQ,OAAO,GAAG,kCAAkC,IACpD;;gCAED,QAAQ,OAAO,kBACd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAqE;;;;;;;;;;;8CAMzF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C,QAAQ,KAAK;;;;;;sDACtE,8OAAC;4CAAI,WAAU;sDAAwC,QAAQ,KAAK;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAAiB,QAAQ,WAAW;;;;;;;;;;;;8CAGnD,8OAAC;oCAAG,WAAU;8CACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;4CAAsB,WAAU;;8DAC/B,8OAAC;oDAAI,WAAU;oDAA0C,MAAK;oDAAe,SAAQ;8DACnF,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;8DAE3J,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;;2CAJ1B;;;;;;;;;;8CASb,8OAAC;oCAAO,WAAW,CAAC,iGAAiG,EACnH,QAAQ,OAAO,GACX,wEACA,yFACJ;8CAAE;;;;;;;2BAlCC;;;;;;;;;;8BA0CX,8OAAC,qIAAA,CAAA,UAAe;oBAAC,WAAU;oBAAc,OAAO;8BAC9C,cAAA,8OAAC;wBAAO,WAAU;;4BAAmK;0CAEnL,8OAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACtE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF", "debugId": null}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Process.tsx"], "sourcesContent": ["import React from 'react';\nimport AnimatedSection from './AnimatedSection';\nimport StaggeredList from './StaggeredList';\n\nconst steps = [\n  {\n    number: '01',\n    title: 'Discovery & Strategy',\n    description: 'We dive deep into your vision and goals, understanding your target audience and business objectives to create the perfect roadmap.',\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n      </svg>\n    )\n  },\n  {\n    number: '02',\n    title: 'Design & Development',\n    description: 'Our expert team builds your app with precision and care, focusing on user experience, performance, and scalability.',\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\" />\n      </svg>\n    )\n  },\n  {\n    number: '03',\n    title: 'Launch & Support',\n    description: 'We handle app store submission, ensure smooth deployment, and provide ongoing support to keep your app running perfectly.',\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n      </svg>\n    )\n  }\n];\n\nexport default function Process() {\n  return (\n    <section id=\"process\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <AnimatedSection className=\"text-center mb-16\">\n          <h2 className=\"text-4xl sm:text-5xl font-bold text-foreground mb-6\">\n            Your Clear Path to <span className=\"text-primary\">Launch</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Our proven process ensures your app is built efficiently,\n            meets your requirements, and launches successfully.\n          </p>\n        </AnimatedSection>\n\n        {/* Process Steps */}\n        <div className=\"relative\">\n          {/* Desktop Layout */}\n          <div className=\"hidden lg:block\">\n            <StaggeredList className=\"flex items-center justify-between\">\n              {steps.map((step, index) => (\n                <div key={index} className=\"flex-1 relative group\">\n                  {/* Step Content */}\n                  <div className=\"text-center transition-all duration-300 group-hover:transform group-hover:-translate-y-2\">\n                    {/* Icon */}\n                    <div className=\"w-20 h-20 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg transition-all duration-300 group-hover:shadow-2xl group-hover:scale-110 group-hover:bg-primary/90\">\n                      {step.icon}\n                    </div>\n                    \n                    {/* Step Number */}\n                    <div className=\"text-6xl font-bold text-gray-200 mb-4\">{step.number}</div>\n                    \n                    {/* Title */}\n                    <h3 className=\"text-2xl font-bold text-foreground mb-4\">{step.title}</h3>\n                    \n                    {/* Description */}\n                    <p className=\"text-gray-600 max-w-sm mx-auto\">{step.description}</p>\n                  </div>\n\n                  {/* Connector Line */}\n                  {index < steps.length - 1 && (\n                    <div className=\"absolute top-10 left-1/2 w-full h-0.5 bg-gray-300 transform translate-x-1/2 z-0\">\n                      <div className=\"absolute right-0 top-1/2 transform -translate-y-1/2\">\n                        <svg className=\"w-4 h-4 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                        </svg>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </StaggeredList>\n          </div>\n\n          {/* Mobile Layout */}\n          <StaggeredList className=\"lg:hidden space-y-12\">\n            {steps.map((step, index) => (\n              <div key={index} className=\"relative\">\n                <div className=\"flex items-start\">\n                  {/* Icon and Number */}\n                  <div className=\"flex-shrink-0 mr-6\">\n                    <div className=\"w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center shadow-lg\">\n                      {step.icon}\n                    </div>\n                    <div className=\"text-4xl font-bold text-gray-200 mt-2 text-center\">{step.number}</div>\n                  </div>\n                  \n                  {/* Content */}\n                  <div className=\"flex-1 pt-2\">\n                    <h3 className=\"text-xl font-bold text-foreground mb-3\">{step.title}</h3>\n                    <p className=\"text-gray-600\">{step.description}</p>\n                  </div>\n                </div>\n\n                {/* Connector Line */}\n                {index < steps.length - 1 && (\n                  <div className=\"absolute left-8 top-20 w-0.5 h-12 bg-gray-300\"></div>\n                )}\n              </div>\n            ))}\n          </StaggeredList>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,QAAQ;IACZ;QACE,QAAQ;QACR,OAAO;QACP,aAAa;QACb,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,QAAQ;QACR,OAAO;QACP,aAAa;QACb,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,QAAQ;QACR,OAAO;QACP,aAAa;QACb,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,qIAAA,CAAA,UAAe;oBAAC,WAAU;;sCACzB,8OAAC;4BAAG,WAAU;;gCAAsD;8CAC/C,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAEpD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,mIAAA,CAAA,UAAa;gCAAC,WAAU;0CACtB,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;wCAAgB,WAAU;;0DAEzB,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI;;;;;;kEAIZ,8OAAC;wDAAI,WAAU;kEAAyC,KAAK,MAAM;;;;;;kEAGnE,8OAAC;wDAAG,WAAU;kEAA2C,KAAK,KAAK;;;;;;kEAGnE,8OAAC;wDAAE,WAAU;kEAAkC,KAAK,WAAW;;;;;;;;;;;;4CAIhE,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAA2I,UAAS;;;;;;;;;;;;;;;;;;;;;;uCAvB/K;;;;;;;;;;;;;;;sCAkChB,8OAAC,mIAAA,CAAA,UAAa;4BAAC,WAAU;sCACtB,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,KAAK,IAAI;;;;;;sEAEZ,8OAAC;4DAAI,WAAU;sEAAqD,KAAK,MAAM;;;;;;;;;;;;8DAIjF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA0C,KAAK,KAAK;;;;;;sEAClE,8OAAC;4DAAE,WAAU;sEAAiB,KAAK,WAAW;;;;;;;;;;;;;;;;;;wCAKjD,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;4CAAI,WAAU;;;;;;;mCAnBT;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BxB", "debugId": null}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/About.tsx"], "sourcesContent": ["import React from 'react';\nimport AnimatedSection from './AnimatedSection';\nimport StaggeredList from './StaggeredList';\n\nexport default function About() {\n  return (\n    <section id=\"about\" className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          {/* Section Header */}\n          <AnimatedSection>\n            <h2 className=\"text-4xl sm:text-5xl font-bold text-foreground mb-6\">\n              We're More Than Just <span className=\"text-primary\">Developers</span>\n            </h2>\n          </AnimatedSection>\n          \n          {/* Mission Statement */}\n          <AnimatedSection className=\"bg-white rounded-2xl p-8 md:p-12 shadow-lg mb-12\" delay={0.2}>\n            <p className=\"text-xl text-gray-700 leading-relaxed mb-8\">\n              At Mobilify, we believe that every great idea deserves to become a reality. \n              We're passionate about helping founders, entrepreneurs, and businesses bridge \n              the gap between vision and execution.\n            </p>\n            \n            <p className=\"text-lg text-gray-600 leading-relaxed mb-8\">\n              Our commitment goes beyond just writing code. We partner with you to understand \n              your goals, challenges, and dreams. We're not satisfied until your app not only \n              works flawlessly but also delights your users and drives your business forward.\n            </p>\n            \n            <StaggeredList className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8\">\n              {/* Value 1 */}\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-semibold text-foreground mb-2\">Quality Craftsmanship</h3>\n                <p className=\"text-gray-600 text-sm\">Every line of code is written with precision and care</p>\n              </div>\n              \n              {/* Value 2 */}\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-semibold text-foreground mb-2\">Client Partnership</h3>\n                <p className=\"text-gray-600 text-sm\">We work alongside you as true partners in success</p>\n              </div>\n              \n              {/* Value 3 */}\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-semibold text-foreground mb-2\">Transparency</h3>\n                <p className=\"text-gray-600 text-sm\">Clear communication and honest timelines, always</p>\n              </div>\n            </StaggeredList>\n          </AnimatedSection>\n          \n          {/* Team Link */}\n          <AnimatedSection className=\"text-center\" delay={0.4}>\n            <p className=\"text-lg text-gray-600 mb-6\">\n              Ready to meet the passionate team behind Mobilify?\n            </p>\n            <button className=\"inline-flex items-center text-primary font-semibold text-lg hover:text-primary/80 transition-colors duration-200 group\">\n              Meet the Team\n              <svg className=\"ml-2 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n              </svg>\n            </button>\n          </AnimatedSection>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,qIAAA,CAAA,UAAe;kCACd,cAAA,8OAAC;4BAAG,WAAU;;gCAAsD;8CAC7C,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;;;;;;kCAKxD,8OAAC,qIAAA,CAAA,UAAe;wBAAC,WAAU;wBAAmD,OAAO;;0CACnF,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAM1D,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAM1D,8OAAC,mIAAA,CAAA,UAAa;gCAAC,WAAU;;kDAEvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAuB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC9E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAC3D,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAIvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAuB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC9E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAC3D,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAIvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAuB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;;sEAC9E,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;sEACrE,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAC3D,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAM3C,8OAAC,qIAAA,CAAA,UAAe;wBAAC,WAAU;wBAAc,OAAO;;0CAC9C,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAO,WAAU;;oCAAyH;kDAEzI,8OAAC;wCAAI,WAAU;wCAAqF,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5I,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Contact.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Contact.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Contact.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 1138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Contact.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Contact.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Contact.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,wCACA", "debugId": null}}, {"offset": {"line": 1152, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/app/page.tsx"], "sourcesContent": ["import Header from '@/components/Header';\nimport Hero from '@/components/Hero';\nimport InteractiveDemo from '@/components/InteractiveDemo';\nimport ServicesOverview from '@/components/ServicesOverview';\nimport Process from '@/components/Process';\nimport About from '@/components/About';\nimport Contact from '@/components/Contact';\nimport Footer from '@/components/Footer';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header />\n\n      <main>\n        <Hero />\n        <InteractiveDemo />\n        <ServicesOverview />\n        <Process />\n        <About />\n        <Contact />\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;;kCACC,8OAAC,0HAAA,CAAA,UAAI;;;;;kCACL,8OAAC,qIAAA,CAAA,UAAe;;;;;kCAChB,8OAAC,sIAAA,CAAA,UAAgB;;;;;kCACjB,8OAAC,6HAAA,CAAA,UAAO;;;;;kCACR,8OAAC,2HAAA,CAAA,UAAK;;;;;kCACN,8OAAC,6HAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;AAIhB", "debugId": null}}, {"offset": {"line": 1269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}