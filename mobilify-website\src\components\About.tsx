import React from 'react';
import AnimatedSection from './AnimatedSection';
import StaggeredList from './StaggeredList';

export default function About() {
  return (
    <section id="about" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          {/* Section Header */}
          <AnimatedSection>
            <h2 className="text-4xl sm:text-5xl font-bold text-foreground mb-6">
              We're More Than Just <span className="text-primary">Developers</span>
            </h2>
          </AnimatedSection>
          
          {/* Mission Statement */}
          <AnimatedSection className="bg-white rounded-2xl p-8 md:p-12 shadow-lg mb-12" delay={0.2}>
            <p className="text-xl text-gray-700 leading-relaxed mb-8">
              At Mobilify, we believe that every great idea deserves to become a reality. 
              We're passionate about helping founders, entrepreneurs, and businesses bridge 
              the gap between vision and execution.
            </p>
            
            <p className="text-lg text-gray-600 leading-relaxed mb-8">
              Our commitment goes beyond just writing code. We partner with you to understand 
              your goals, challenges, and dreams. We're not satisfied until your app not only 
              works flawlessly but also delights your users and drives your business forward.
            </p>
            
            <StaggeredList className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
              {/* Value 1 */}
              <div className="text-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">Quality Craftsmanship</h3>
                <p className="text-gray-600 text-sm">Every line of code is written with precision and care</p>
              </div>
              
              {/* Value 2 */}
              <div className="text-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">Client Partnership</h3>
                <p className="text-gray-600 text-sm">We work alongside you as true partners in success</p>
              </div>
              
              {/* Value 3 */}
              <div className="text-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">Transparency</h3>
                <p className="text-gray-600 text-sm">Clear communication and honest timelines, always</p>
              </div>
            </StaggeredList>
          </AnimatedSection>
          
          {/* Team Link */}
          <AnimatedSection className="text-center" delay={0.4}>
            <p className="text-lg text-gray-600 mb-6">
              Ready to meet the passionate team behind Mobilify?
            </p>
            <button className="inline-flex items-center text-primary font-semibold text-lg hover:text-primary/80 transition-colors duration-200 group">
              Meet the Team
              <svg className="ml-2 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </button>
          </AnimatedSection>
        </div>
      </div>
    </section>
  );
}
