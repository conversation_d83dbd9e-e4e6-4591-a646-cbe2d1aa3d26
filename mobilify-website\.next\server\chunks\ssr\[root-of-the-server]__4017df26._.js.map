{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Logo.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LogoProps {\n  className?: string;\n}\n\nexport default function Logo({ className = \"\" }: LogoProps) {\n  return (\n    <div className={`w-10 h-10 bg-logo-bg rounded-lg flex items-center justify-center ${className}`}>\n      <span className=\"text-white font-bold text-xl\">M</span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAMe,SAAS,KAAK,EAAE,YAAY,EAAE,EAAa;IACxD,qBACE,8OAAC;QAAI,WAAW,CAAC,iEAAiE,EAAE,WAAW;kBAC7F,cAAA,8OAAC;YAAK,WAAU;sBAA+B;;;;;;;;;;;AAGrD", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Logo from './Logo';\n\nexport default function Header() {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    setIsMobileMenuOpen(false); // Close mobile menu after navigation\n  };\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-b border-gray-100 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <a href=\"/\" className=\"flex items-center hover:opacity-80 transition-opacity duration-200\">\n            <Logo />\n            <span className=\"ml-3 text-xl font-bold text-foreground\">Mobilify</span>\n          </a>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <a\n              href=\"/services\"\n              className=\"text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              Services\n            </a>\n            <button\n              onClick={() => scrollToSection('process')}\n              className=\"text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              How It Works\n            </button>\n            <a\n              href=\"/about\"\n              className=\"text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              About Us\n            </a>\n          </nav>\n\n          {/* Desktop CTA Button */}\n          <button\n            onClick={() => scrollToSection('contact')}\n            className=\"hidden md:block bg-primary text-white px-6 py-2 rounded-lg font-semibold hover:bg-primary/90 transition-colors duration-200\"\n          >\n            Get a Quote\n          </button>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\"\n          >\n            <div className=\"w-6 h-6 flex flex-col justify-center items-center\">\n              <span className={`w-6 h-0.5 bg-foreground block transition-all duration-300 origin-center ${isMobileMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`} />\n              <span className={`w-6 h-0.5 bg-foreground block mt-1.5 transition-all duration-300 ${isMobileMenuOpen ? 'opacity-0' : ''}`} />\n              <span className={`w-6 h-0.5 bg-foreground block mt-1.5 transition-all duration-300 origin-center ${isMobileMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`} />\n            </div>\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      {isMobileMenuOpen && (\n        <div className=\"md:hidden bg-white border-t border-gray-100 shadow-lg transition-all duration-300\">\n          <div className=\"max-w-7xl mx-auto px-4 py-6 space-y-4\">\n            <a\n              href=\"/services\"\n              className=\"block w-full text-left py-3 text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              Services\n            </a>\n            <button\n              onClick={() => scrollToSection('process')}\n              className=\"block w-full text-left py-3 text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              How It Works\n            </button>\n            <a\n              href=\"/about\"\n              className=\"block w-full text-left py-3 text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              About Us\n            </a>\n            <button\n              onClick={() => scrollToSection('contact')}\n              className=\"w-full bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors duration-200 mt-4\"\n            >\n              Get a Quote\n            </button>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,oBAAoB,QAAQ,qCAAqC;IACnE;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAE,MAAK;4BAAI,WAAU;;8CACpB,8OAAC,0HAAA,CAAA,UAAI;;;;;8CACL,8OAAC;oCAAK,WAAU;8CAAyC;;;;;;;;;;;;sCAI3D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAU;sCACX;;;;;;sCAKD,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAW,CAAC,wEAAwE,EAAE,mBAAmB,8BAA8B,IAAI;;;;;;kDACjJ,8OAAC;wCAAK,WAAW,CAAC,iEAAiE,EAAE,mBAAmB,cAAc,IAAI;;;;;;kDAC1H,8OAAC;wCAAK,WAAW,CAAC,+EAA+E,EAAE,mBAAmB,gCAAgC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOjK,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/AnimatedSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface AnimatedSectionProps {\n  children: React.ReactNode;\n  className?: string;\n  delay?: number;\n  direction?: 'up' | 'down' | 'left' | 'right';\n}\n\nexport default function AnimatedSection({\n  children,\n  className = '',\n  delay = 0,\n  direction = 'up'\n}: AnimatedSectionProps) {\n  // Temporary: Return a simple div without animations\n  // TODO: Add framer-motion animations later\n  return (\n    <div className={className}>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAWe,SAAS,gBAAgB,EACtC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,CAAC,EACT,YAAY,IAAI,EACK;IACrB,oDAAoD;IACpD,2CAA2C;IAC3C,qBACE,8OAAC;QAAI,WAAW;kBACb;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/StaggeredList.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface StaggeredListProps {\n  children: React.ReactNode[];\n  className?: string;\n  staggerDelay?: number;\n}\n\nexport default function StaggeredList({\n  children,\n  className = '',\n  staggerDelay = 0.1\n}: StaggeredListProps) {\n  // Temporary: Return a simple div without animations\n  // TODO: Add framer-motion staggered animations later\n  return (\n    <div className={className}>\n      {children.map((child, index) => (\n        <div key={index}>\n          {child}\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAUe,SAAS,cAAc,EACpC,QAAQ,EACR,YAAY,EAAE,EACd,eAAe,GAAG,EACC;IACnB,oDAAoD;IACpD,qDAAqD;IACrD,qBACE,8OAAC;QAAI,WAAW;kBACb,SAAS,GAAG,CAAC,CAAC,OAAO,sBACpB,8OAAC;0BACE;eADO;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}