{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Logo.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LogoProps {\n  className?: string;\n}\n\nexport default function Logo({ className = \"\" }: LogoProps) {\n  return (\n    <div className={`w-10 h-10 bg-logo-bg rounded-lg flex items-center justify-center ${className}`}>\n      <span className=\"text-white font-bold text-xl\">M</span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAMe,SAAS,KAAK,EAAE,YAAY,EAAE,EAAa;IACxD,qBACE,6LAAC;QAAI,WAAW,CAAC,iEAAiE,EAAE,WAAW;kBAC7F,cAAA,6LAAC;YAAK,WAAU;sBAA+B;;;;;;;;;;;AAGrD;KANwB", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Logo from './Logo';\n\nexport default function Header() {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    setIsMobileMenuOpen(false); // Close mobile menu after navigation\n  };\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-b border-gray-100 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <a href=\"/\" className=\"flex items-center hover:opacity-80 transition-opacity duration-200\">\n            <Logo />\n            <span className=\"ml-3 text-xl font-bold text-foreground\">Mobilify</span>\n          </a>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <a\n              href=\"/services\"\n              className=\"text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              Services\n            </a>\n            <button\n              onClick={() => scrollToSection('process')}\n              className=\"text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              How It Works\n            </button>\n            <a\n              href=\"/about\"\n              className=\"text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              About Us\n            </a>\n          </nav>\n\n          {/* Desktop CTA Button */}\n          <button\n            onClick={() => scrollToSection('contact')}\n            className=\"hidden md:block bg-primary text-white px-6 py-2 rounded-lg font-semibold hover:bg-primary/90 transition-colors duration-200\"\n          >\n            Get a Quote\n          </button>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\"\n          >\n            <div className=\"w-6 h-6 flex flex-col justify-center items-center\">\n              <span className={`w-6 h-0.5 bg-foreground block transition-all duration-300 origin-center ${isMobileMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`} />\n              <span className={`w-6 h-0.5 bg-foreground block mt-1.5 transition-all duration-300 ${isMobileMenuOpen ? 'opacity-0' : ''}`} />\n              <span className={`w-6 h-0.5 bg-foreground block mt-1.5 transition-all duration-300 origin-center ${isMobileMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`} />\n            </div>\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      {isMobileMenuOpen && (\n        <div className=\"md:hidden bg-white border-t border-gray-100 shadow-lg transition-all duration-300\">\n          <div className=\"max-w-7xl mx-auto px-4 py-6 space-y-4\">\n            <a\n              href=\"/services\"\n              className=\"block w-full text-left py-3 text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              Services\n            </a>\n            <button\n              onClick={() => scrollToSection('process')}\n              className=\"block w-full text-left py-3 text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              How It Works\n            </button>\n            <a\n              href=\"/about\"\n              className=\"block w-full text-left py-3 text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              About Us\n            </a>\n            <button\n              onClick={() => scrollToSection('contact')}\n              className=\"w-full bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors duration-200 mt-4\"\n            >\n              Get a Quote\n            </button>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,oBAAoB,QAAQ,qCAAqC;IACnE;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAE,MAAK;4BAAI,WAAU;;8CACpB,6LAAC,6HAAA,CAAA,UAAI;;;;;8CACL,6LAAC;oCAAK,WAAU;8CAAyC;;;;;;;;;;;;sCAI3D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAU;sCACX;;;;;;sCAKD,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,CAAC,wEAAwE,EAAE,mBAAmB,8BAA8B,IAAI;;;;;;kDACjJ,6LAAC;wCAAK,WAAW,CAAC,iEAAiE,EAAE,mBAAmB,cAAc,IAAI;;;;;;kDAC1H,6LAAC;wCAAK,WAAW,CAAC,+EAA+E,EAAE,mBAAmB,gCAAgC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOjK,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAlGwB;KAAA", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/AnimatedSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface AnimatedSectionProps {\n  children: React.ReactNode;\n  className?: string;\n  delay?: number;\n  direction?: 'up' | 'down' | 'left' | 'right';\n}\n\nexport default function AnimatedSection({\n  children,\n  className = '',\n  delay = 0,\n  direction = 'up'\n}: AnimatedSectionProps) {\n  // Temporary: Return a simple div without animations\n  // TODO: Add framer-motion animations later\n  return (\n    <div className={className}>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAWe,SAAS,gBAAgB,EACtC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,CAAC,EACT,YAAY,IAAI,EACK;IACrB,oDAAoD;IACpD,2CAA2C;IAC3C,qBACE,6LAAC;QAAI,WAAW;kBACb;;;;;;AAGP;KAbwB", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport AnimatedSection from './AnimatedSection';\n\nexport default function Hero() {\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"hero\" className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Column - Content */}\n          <AnimatedSection className=\"text-center lg:text-left\">\n            <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight mb-6\">\n              Your Idea.{' '}\n              <span className=\"text-primary\">Your App.</span>{' '}\n              Realized.\n            </h1>\n\n            <p className=\"text-xl text-gray-600 mb-8 leading-relaxed\">\n              Mobilify transforms your concepts and existing websites into stunning,\n              high-performance mobile apps. We are the bridge from vision to launch.\n            </p>\n\n            <button\n              onClick={() => scrollToSection('demo')}\n              className=\"bg-primary text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary/90 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl\"\n            >\n              See How It Works\n            </button>\n          </AnimatedSection>\n\n          {/* Right Column - Visual Placeholder */}\n          <AnimatedSection className=\"flex justify-center lg:justify-end\" delay={0.4} direction=\"right\">\n            <div className=\"relative\">\n              {/* Phone Mockup Placeholder */}\n              <div className=\"w-64 h-96 bg-gray-800 rounded-3xl p-2 shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500\">\n                <div className=\"w-full h-full bg-white rounded-2xl flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <div className=\"w-16 h-16 bg-primary rounded-full mx-auto mb-4 flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-2xl\">M</span>\n                    </div>\n                    <p className=\"text-gray-600 text-sm\">App Preview</p>\n                    <p className=\"text-gray-400 text-xs mt-2\">Interactive Demo Below</p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Floating Elements */}\n              <div className=\"absolute -top-4 -left-4 w-8 h-8 bg-primary rounded-full opacity-20 animate-pulse\"></div>\n              <div className=\"absolute -bottom-4 -right-4 w-6 h-6 bg-primary rounded-full opacity-30 animate-pulse delay-300\"></div>\n            </div>\n          </AnimatedSection>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS;IACtB,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,wIAAA,CAAA,UAAe;wBAAC,WAAU;;0CACzB,6LAAC;gCAAG,WAAU;;oCAAgF;oCACjF;kDACX,6LAAC;wCAAK,WAAU;kDAAe;;;;;;oCAAiB;oCAAI;;;;;;;0CAItD,6LAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAK1D,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;;kCAMH,6LAAC,wIAAA,CAAA,UAAe;wBAAC,WAAU;wBAAqC,OAAO;wBAAK,WAAU;kCACpF,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAgC;;;;;;;;;;;8DAElD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;8CAMhD,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B;KA1DwB", "debugId": null}}, {"offset": {"line": 478, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/InteractiveDemo.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport AnimatedSection from './AnimatedSection';\n\nexport default function InteractiveDemo() {\n  const [activeTab, setActiveTab] = useState<'website' | 'idea'>('website');\n  const [inputValue, setInputValue] = useState('');\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [showResult, setShowResult] = useState(false);\n\n  const handleGenerate = () => {\n    if (!inputValue.trim()) return;\n\n    setIsGenerating(true);\n    setShowResult(false);\n\n    // Simulate generation process\n    setTimeout(() => {\n      setIsGenerating(false);\n      setShowResult(true);\n    }, 3000);\n  };\n\n  return (\n    <section id=\"demo\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <AnimatedSection className=\"text-center mb-16\">\n          <h2 className=\"text-4xl sm:text-5xl font-bold text-foreground mb-6\">\n            From Zero to App, <span className=\"text-primary\">Instantly</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Experience the power of our platform. Enter a website URL or describe your idea,\n            and watch as we generate a beautiful mobile app preview.\n          </p>\n        </AnimatedSection>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Column - Interactive Tool */}\n          <AnimatedSection className=\"space-y-6\" direction=\"left\">\n            {/* Tabs */}\n            <div className=\"flex bg-gray-100 rounded-lg p-1\">\n              <button\n                onClick={() => setActiveTab('website')}\n                className={`flex-1 py-3 px-4 rounded-md font-semibold transition-all duration-200 ${\n                  activeTab === 'website'\n                    ? 'bg-white text-primary shadow-sm'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                Convert a Website\n              </button>\n              <button\n                onClick={() => setActiveTab('idea')}\n                className={`flex-1 py-3 px-4 rounded-md font-semibold transition-all duration-200 ${\n                  activeTab === 'idea'\n                    ? 'bg-white text-primary shadow-sm'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                Describe an Idea\n              </button>\n            </div>\n\n            {/* Input Field */}\n            <div className=\"space-y-4\">\n              <label className=\"block text-sm font-medium text-gray-700\">\n                {activeTab === 'website' ? 'Website URL' : 'Your App Idea'}\n              </label>\n              <input\n                type={activeTab === 'website' ? 'url' : 'text'}\n                value={inputValue}\n                onChange={(e) => setInputValue(e.target.value)}\n                placeholder={\n                  activeTab === 'website'\n                    ? 'https://your-website.com'\n                    : 'Describe your app idea in a few words...'\n                }\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200\"\n              />\n            </div>\n\n            {/* Generate Button */}\n            <button\n              onClick={handleGenerate}\n              disabled={!inputValue.trim() || isGenerating}\n              className=\"w-full bg-primary text-white py-4 rounded-lg font-semibold text-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95\"\n            >\n              {isGenerating ? (\n                <span className=\"flex items-center justify-center\">\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  Generating...\n                </span>\n              ) : (\n                'Mobilify Preview'\n              )}\n            </button>\n          </AnimatedSection>\n\n          {/* Right Column - Phone Mockup */}\n          <AnimatedSection className=\"flex justify-center\" direction=\"right\" delay={0.3}>\n            <div className=\"relative\">\n              {/* Phone Frame */}\n              <div className=\"w-72 h-96 bg-gray-900 rounded-3xl p-3 shadow-2xl transform perspective-1000 rotate-y-12\">\n                <div className=\"w-full h-full bg-white rounded-2xl overflow-hidden relative\">\n                  {isGenerating ? (\n                    // Loading State\n                    <div className=\"flex items-center justify-center h-full\">\n                      <div className=\"text-center\">\n                        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n                        <p className=\"text-gray-600\">Creating your app...</p>\n                      </div>\n                    </div>\n                  ) : showResult ? (\n                    // Generated App State\n                    <div className=\"p-6 h-full flex flex-col\">\n                      {/* App Header */}\n                      <div className=\"text-center mb-6\">\n                        <div className=\"w-12 h-12 bg-primary rounded-full mx-auto mb-3 flex items-center justify-center\">\n                          <span className=\"text-white font-bold\">M</span>\n                        </div>\n                        <h3 className=\"font-semibold text-gray-800\">Your App</h3>\n                        <p className=\"text-sm text-gray-500\">Generated Successfully!</p>\n                      </div>\n\n                      {/* App Content */}\n                      <div className=\"space-y-4 flex-1\">\n                        <div className=\"h-4 bg-primary/20 rounded w-3/4\"></div>\n                        <div className=\"h-4 bg-primary/20 rounded w-1/2\"></div>\n                        <div className=\"h-20 bg-primary/10 rounded\"></div>\n                        <div className=\"h-4 bg-primary/20 rounded w-2/3\"></div>\n                      </div>\n\n                      {/* Bottom Navigation */}\n                      <div className=\"flex justify-around pt-4 border-t border-gray-200\">\n                        <div className=\"w-6 h-6 bg-gray-300 rounded\"></div>\n                        <div className=\"w-6 h-6 bg-gray-300 rounded\"></div>\n                        <div className=\"w-6 h-6 bg-primary rounded\"></div>\n                        <div className=\"w-6 h-6 bg-gray-300 rounded\"></div>\n                      </div>\n                    </div>\n                  ) : (\n                    // Default State\n                    <div className=\"p-6 h-full flex flex-col\">\n                      <div className=\"text-center mb-6\">\n                        <div className=\"w-12 h-12 bg-gray-200 rounded-full mx-auto mb-3\"></div>\n                        <h3 className=\"font-semibold text-gray-800\">Your App Preview</h3>\n                        <p className=\"text-sm text-gray-500\">Will appear here</p>\n                      </div>\n\n                      {/* Placeholder UI Elements */}\n                      <div className=\"space-y-4 flex-1\">\n                        <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                        <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n                        <div className=\"h-20 bg-gray-100 rounded\"></div>\n                        <div className=\"h-4 bg-gray-200 rounded w-2/3\"></div>\n                      </div>\n\n                      {/* Bottom Navigation Placeholder */}\n                      <div className=\"flex justify-around pt-4 border-t border-gray-200\">\n                        <div className=\"w-6 h-6 bg-gray-300 rounded\"></div>\n                        <div className=\"w-6 h-6 bg-gray-300 rounded\"></div>\n                        <div className=\"w-6 h-6 bg-primary rounded\"></div>\n                        <div className=\"w-6 h-6 bg-gray-300 rounded\"></div>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </AnimatedSection>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,iBAAiB;QACrB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,gBAAgB;QAChB,cAAc;QAEd,8BAA8B;QAC9B,WAAW;YACT,gBAAgB;YAChB,cAAc;QAChB,GAAG;IACL;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,wIAAA,CAAA,UAAe;oBAAC,WAAU;;sCACzB,6LAAC;4BAAG,WAAU;;gCAAsD;8CAChD,6LAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAEnD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,wIAAA,CAAA,UAAe;4BAAC,WAAU;4BAAY,WAAU;;8CAE/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,sEAAsE,EAChF,cAAc,YACV,oCACA,qCACJ;sDACH;;;;;;sDAGD,6LAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,sEAAsE,EAChF,cAAc,SACV,oCACA,qCACJ;sDACH;;;;;;;;;;;;8CAMH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDACd,cAAc,YAAY,gBAAgB;;;;;;sDAE7C,6LAAC;4CACC,MAAM,cAAc,YAAY,QAAQ;4CACxC,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,aACE,cAAc,YACV,6BACA;4CAEN,WAAU;;;;;;;;;;;;8CAKd,6LAAC;oCACC,SAAS;oCACT,UAAU,CAAC,WAAW,IAAI,MAAM;oCAChC,WAAU;8CAET,6BACC,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAI,WAAU;gDAA6C,OAAM;gDAA6B,MAAK;gDAAO,SAAQ;;kEACjH,6LAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,6LAAC;wDAAK,WAAU;wDAAa,MAAK;wDAAe,GAAE;;;;;;;;;;;;4CAC/C;;;;;;+CAIR;;;;;;;;;;;;sCAMN,6LAAC,wIAAA,CAAA,UAAe;4BAAC,WAAU;4BAAsB,WAAU;4BAAQ,OAAO;sCACxE,cAAA,6LAAC;gCAAI,WAAU;0CAEb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,eACC,gBAAgB;sDAChB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;mDAG/B,aACF,sBAAsB;sDACtB,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAAuB;;;;;;;;;;;sEAEzC,6LAAC;4DAAG,WAAU;sEAA8B;;;;;;sEAC5C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAIvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAIjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;mDAInB,gBAAgB;sDAChB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAG,WAAU;sEAA8B;;;;;;sEAC5C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAIvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAIjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvC;GA9KwB;KAAA", "debugId": null}}, {"offset": {"line": 997, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/StaggeredList.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface StaggeredListProps {\n  children: React.ReactNode[];\n  className?: string;\n  staggerDelay?: number;\n}\n\nexport default function StaggeredList({\n  children,\n  className = '',\n  staggerDelay = 0.1\n}: StaggeredListProps) {\n  // Temporary: Return a simple div without animations\n  // TODO: Add framer-motion staggered animations later\n  return (\n    <div className={className}>\n      {children.map((child, index) => (\n        <div key={index}>\n          {child}\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAUe,SAAS,cAAc,EACpC,QAAQ,EACR,YAAY,EAAE,EACd,eAAe,GAAG,EACC;IACnB,oDAAoD;IACpD,qDAAqD;IACrD,qBACE,6LAAC;QAAI,WAAW;kBACb,SAAS,GAAG,CAAC,CAAC,OAAO,sBACpB,6LAAC;0BACE;eADO;;;;;;;;;;AAMlB;KAhBwB", "debugId": null}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport AnimatedSection from './AnimatedSection';\n\nexport default function Contact() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    project: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n  const [errors, setErrors] = useState({\n    name: '',\n    email: '',\n    project: ''\n  });\n\n  const validateField = (name: string, value: string) => {\n    switch (name) {\n      case 'name':\n        return value.trim().length < 2 ? 'Name must be at least 2 characters' : '';\n      case 'email':\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        return !emailRegex.test(value) ? 'Please enter a valid email address' : '';\n      case 'project':\n        return value.trim().length < 10 ? 'Please provide at least 10 characters describing your project' : '';\n      default:\n        return '';\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Clear error when user starts typing\n    if (errors[name as keyof typeof errors]) {\n      setErrors({\n        ...errors,\n        [name]: ''\n      });\n    }\n  };\n\n  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    const error = validateField(name, value);\n    setErrors({\n      ...errors,\n      [name]: error\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Validate all fields\n    const newErrors = {\n      name: validateField('name', formData.name),\n      email: validateField('email', formData.email),\n      project: validateField('project', formData.project)\n    };\n\n    setErrors(newErrors);\n\n    // Check if there are any errors\n    if (Object.values(newErrors).some(error => error !== '')) {\n      return;\n    }\n\n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n\n    try {\n      // Simulate form submission (replace with actual Web3Forms integration)\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // For demo purposes, we'll just show success\n      setSubmitStatus('success');\n      setFormData({ name: '', email: '', project: '' });\n      setErrors({ name: '', email: '', project: '' });\n\n      // TODO: Replace with actual Web3Forms integration\n      // const response = await fetch('https://api.web3forms.com/submit', {\n      //   method: 'POST',\n      //   headers: {\n      //     'Content-Type': 'application/json',\n      //   },\n      //   body: JSON.stringify({\n      //     access_key: 'YOUR_WEB3FORMS_ACCESS_KEY',\n      //     name: formData.name,\n      //     email: formData.email,\n      //     message: formData.project,\n      //     subject: 'New Mobilify Contact Form Submission',\n      //     from_name: 'Mobilify Website',\n      //     to_email: '<EMAIL>'\n      //   }),\n      // });\n    } catch (error) {\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Section Header */}\n          <AnimatedSection className=\"text-center mb-16\">\n            <h2 className=\"text-4xl sm:text-5xl font-bold text-foreground mb-6\">\n              Ready to Build Your <span className=\"text-primary\">Mobile Future?</span>\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Let's discuss your project. We're happy to provide a free,\n              no-obligation consultation and quote.\n            </p>\n          </AnimatedSection>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n            {/* Contact Form */}\n            <AnimatedSection className=\"bg-gray-50 rounded-2xl p-8\" direction=\"left\">\n              <h3 className=\"text-2xl font-bold text-foreground mb-6\">Get Started Today</h3>\n              \n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                {/* Name Field */}\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Your Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleChange}\n                    onBlur={handleBlur}\n                    required\n                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-all duration-200 ${\n                      errors.name\n                        ? 'border-red-300 focus:ring-red-500'\n                        : 'border-gray-300 focus:ring-primary'\n                    }`}\n                    placeholder=\"John Doe\"\n                  />\n                  {errors.name && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>\n                  )}\n                </div>\n\n                {/* Email Field */}\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Email Address *\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    onBlur={handleBlur}\n                    required\n                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-all duration-200 ${\n                      errors.email\n                        ? 'border-red-300 focus:ring-red-500'\n                        : 'border-gray-300 focus:ring-primary'\n                    }`}\n                    placeholder=\"<EMAIL>\"\n                  />\n                  {errors.email && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.email}</p>\n                  )}\n                </div>\n\n                {/* Project Description */}\n                <div>\n                  <label htmlFor=\"project\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Briefly describe your project *\n                  </label>\n                  <textarea\n                    id=\"project\"\n                    name=\"project\"\n                    value={formData.project}\n                    onChange={handleChange}\n                    onBlur={handleBlur}\n                    required\n                    rows={4}\n                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-all duration-200 resize-none ${\n                      errors.project\n                        ? 'border-red-300 focus:ring-red-500'\n                        : 'border-gray-300 focus:ring-primary'\n                    }`}\n                    placeholder=\"Tell us about your app idea, target audience, key features, or website you'd like to convert...\"\n                  />\n                  {errors.project && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.project}</p>\n                  )}\n                </div>\n\n                {/* Submit Button */}\n                <button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className=\"w-full bg-primary text-white py-4 rounded-lg font-semibold text-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105\"\n                >\n                  {isSubmitting ? (\n                    <span className=\"flex items-center justify-center\">\n                      <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                      Sending...\n                    </span>\n                  ) : (\n                    'Send Message'\n                  )}\n                </button>\n\n                {/* Status Messages */}\n                {submitStatus === 'success' && (\n                  <div className=\"p-4 bg-green-50 border border-green-200 rounded-lg\">\n                    <p className=\"text-green-800 text-sm\">\n                      ✅ Thank you! Your message has been sent successfully. We'll get back to you within 24 hours.\n                    </p>\n                  </div>\n                )}\n\n                {submitStatus === 'error' && (\n                  <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\n                    <p className=\"text-red-800 text-sm\">\n                      ❌ Sorry, there was an error sending your message. Please try again or contact us directly.\n                    </p>\n                  </div>\n                )}\n              </form>\n            </AnimatedSection>\n\n            {/* Contact Information */}\n            <AnimatedSection className=\"space-y-8\" direction=\"right\" delay={0.2}>\n              <div>\n                <h3 className=\"text-2xl font-bold text-foreground mb-6\">Let's Connect</h3>\n                <p className=\"text-gray-600 mb-8\">\n                  Have questions? Want to discuss your project in detail? \n                  We're here to help you turn your vision into reality.\n                </p>\n              </div>\n\n              {/* Contact Methods */}\n              <div className=\"space-y-6\">\n                <div className=\"flex items-start\">\n                  <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                    <svg className=\"w-6 h-6 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-foreground mb-1\">Email Us</h4>\n                    <p className=\"text-gray-600\"><EMAIL></p>\n                    <p className=\"text-sm text-gray-500\">We respond within 24 hours</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                    <svg className=\"w-6 h-6 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-foreground mb-1\">Response Time</h4>\n                    <p className=\"text-gray-600\">Within 24 hours</p>\n                    <p className=\"text-sm text-gray-500\">Usually much faster!</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                    <svg className=\"w-6 h-6 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-foreground mb-1\">Free Consultation</h4>\n                    <p className=\"text-gray-600\">No obligation quote</p>\n                    <p className=\"text-sm text-gray-500\">Let's discuss your project</p>\n                  </div>\n                </div>\n              </div>\n            </AnimatedSection>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAC/E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,MAAM;QACN,OAAO;QACP,SAAS;IACX;IAEA,MAAM,gBAAgB,CAAC,MAAc;QACnC,OAAQ;YACN,KAAK;gBACH,OAAO,MAAM,IAAI,GAAG,MAAM,GAAG,IAAI,uCAAuC;YAC1E,KAAK;gBACH,MAAM,aAAa;gBACnB,OAAO,CAAC,WAAW,IAAI,CAAC,SAAS,uCAAuC;YAC1E,KAAK;gBACH,OAAO,MAAM,IAAI,GAAG,MAAM,GAAG,KAAK,kEAAkE;YACtG;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,KAAK,EAAE;QACV;QAEA,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAA4B,EAAE;YACvC,UAAU;gBACR,GAAG,MAAM;gBACT,CAAC,KAAK,EAAE;YACV;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,MAAM,QAAQ,cAAc,MAAM;QAClC,UAAU;YACR,GAAG,MAAM;YACT,CAAC,KAAK,EAAE;QACV;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,sBAAsB;QACtB,MAAM,YAAY;YAChB,MAAM,cAAc,QAAQ,SAAS,IAAI;YACzC,OAAO,cAAc,SAAS,SAAS,KAAK;YAC5C,SAAS,cAAc,WAAW,SAAS,OAAO;QACpD;QAEA,UAAU;QAEV,gCAAgC;QAChC,IAAI,OAAO,MAAM,CAAC,WAAW,IAAI,CAAC,CAAA,QAAS,UAAU,KAAK;YACxD;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,uEAAuE;YACvE,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,6CAA6C;YAC7C,gBAAgB;YAChB,YAAY;gBAAE,MAAM;gBAAI,OAAO;gBAAI,SAAS;YAAG;YAC/C,UAAU;gBAAE,MAAM;gBAAI,OAAO;gBAAI,SAAS;YAAG;QAE7C,kDAAkD;QAClD,qEAAqE;QACrE,oBAAoB;QACpB,eAAe;QACf,0CAA0C;QAC1C,OAAO;QACP,2BAA2B;QAC3B,+CAA+C;QAC/C,2BAA2B;QAC3B,6BAA6B;QAC7B,iCAAiC;QACjC,uDAAuD;QACvD,qCAAqC;QACrC,uCAAuC;QACvC,QAAQ;QACR,MAAM;QACR,EAAE,OAAO,OAAO;YACd,gBAAgB;QAClB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,wIAAA,CAAA,UAAe;wBAAC,WAAU;;0CACzB,6LAAC;gCAAG,WAAU;;oCAAsD;kDAC9C,6LAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAErD,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,wIAAA,CAAA,UAAe;gCAAC,WAAU;gCAA6B,WAAU;;kDAChE,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDAExD,6LAAC;wCAAK,UAAU;wCAAc,WAAU;;0DAEtC,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAO,WAAU;kEAA+C;;;;;;kEAG/E,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU;wDACV,QAAQ;wDACR,QAAQ;wDACR,WAAW,CAAC,qGAAqG,EAC/G,OAAO,IAAI,GACP,sCACA,sCACJ;wDACF,aAAY;;;;;;oDAEb,OAAO,IAAI,kBACV,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,IAAI;;;;;;;;;;;;0DAKzD,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAA+C;;;;;;kEAGhF,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,QAAQ;wDACR,QAAQ;wDACR,WAAW,CAAC,qGAAqG,EAC/G,OAAO,KAAK,GACR,sCACA,sCACJ;wDACF,aAAY;;;;;;oDAEb,OAAO,KAAK,kBACX,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,KAAK;;;;;;;;;;;;0DAK1D,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAA+C;;;;;;kEAGlF,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,QAAQ;wDACR,QAAQ;wDACR,MAAM;wDACN,WAAW,CAAC,iHAAiH,EAC3H,OAAO,OAAO,GACV,sCACA,sCACJ;wDACF,aAAY;;;;;;oDAEb,OAAO,OAAO,kBACb,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,OAAO;;;;;;;;;;;;0DAK5D,6LAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,6BACC,6LAAC;oDAAK,WAAU;;sEACd,6LAAC;4DAAI,WAAU;4DAA6C,OAAM;4DAA6B,MAAK;4DAAO,SAAQ;;8EACjH,6LAAC;oEAAO,WAAU;oEAAa,IAAG;oEAAK,IAAG;oEAAK,GAAE;oEAAK,QAAO;oEAAe,aAAY;;;;;;8EACxF,6LAAC;oEAAK,WAAU;oEAAa,MAAK;oEAAe,GAAE;;;;;;;;;;;;wDAC/C;;;;;;2DAIR;;;;;;4CAKH,iBAAiB,2BAChB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;4CAMzC,iBAAiB,yBAChB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DAAuB;;;;;;;;;;;;;;;;;;;;;;;0CAS5C,6LAAC,wIAAA,CAAA,UAAe;gCAAC,WAAU;gCAAY,WAAU;gCAAQ,OAAO;;kDAC9D,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA0C;;;;;;0DACxD,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAOpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAqC;;;;;;0EACnD,6LAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAqC;;;;;;0EACnD,6LAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAqC;;;;;;0EACnD,6LAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD;GAxSwB;KAAA", "debugId": null}}]}