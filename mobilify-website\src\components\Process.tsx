import React from 'react';
import AnimatedSection from './AnimatedSection';
import StaggeredList from './StaggeredList';

const steps = [
  {
    number: '01',
    title: 'Discovery & Strategy',
    description: 'We dive deep into your vision and goals, understanding your target audience and business objectives to create the perfect roadmap.',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
      </svg>
    )
  },
  {
    number: '02',
    title: 'Design & Development',
    description: 'Our expert team builds your app with precision and care, focusing on user experience, performance, and scalability.',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
      </svg>
    )
  },
  {
    number: '03',
    title: 'Launch & Support',
    description: 'We handle app store submission, ensure smooth deployment, and provide ongoing support to keep your app running perfectly.',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
      </svg>
    )
  }
];

export default function Process() {
  return (
    <section id="process" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <AnimatedSection className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold text-foreground mb-6">
            Your Clear Path to <span className="text-primary">Launch</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our proven process ensures your app is built efficiently,
            meets your requirements, and launches successfully.
          </p>
        </AnimatedSection>

        {/* Process Steps */}
        <div className="relative">
          {/* Desktop Layout */}
          <div className="hidden lg:block">
            <StaggeredList className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={index} className="flex-1 relative">
                  {/* Step Content */}
                  <div className="text-center">
                    {/* Icon */}
                    <div className="w-20 h-20 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                      {step.icon}
                    </div>
                    
                    {/* Step Number */}
                    <div className="text-6xl font-bold text-gray-200 mb-4">{step.number}</div>
                    
                    {/* Title */}
                    <h3 className="text-2xl font-bold text-foreground mb-4">{step.title}</h3>
                    
                    {/* Description */}
                    <p className="text-gray-600 max-w-sm mx-auto">{step.description}</p>
                  </div>

                  {/* Connector Line */}
                  {index < steps.length - 1 && (
                    <div className="absolute top-10 left-1/2 w-full h-0.5 bg-gray-300 transform translate-x-1/2 z-0">
                      <div className="absolute right-0 top-1/2 transform -translate-y-1/2">
                        <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </StaggeredList>
          </div>

          {/* Mobile Layout */}
          <StaggeredList className="lg:hidden space-y-12">
            {steps.map((step, index) => (
              <div key={index} className="relative">
                <div className="flex items-start">
                  {/* Icon and Number */}
                  <div className="flex-shrink-0 mr-6">
                    <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center shadow-lg">
                      {step.icon}
                    </div>
                    <div className="text-4xl font-bold text-gray-200 mt-2 text-center">{step.number}</div>
                  </div>
                  
                  {/* Content */}
                  <div className="flex-1 pt-2">
                    <h3 className="text-xl font-bold text-foreground mb-3">{step.title}</h3>
                    <p className="text-gray-600">{step.description}</p>
                  </div>
                </div>

                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className="absolute left-8 top-20 w-0.5 h-12 bg-gray-300"></div>
                )}
              </div>
            ))}
          </StaggeredList>
        </div>
      </div>
    </section>
  );
}
