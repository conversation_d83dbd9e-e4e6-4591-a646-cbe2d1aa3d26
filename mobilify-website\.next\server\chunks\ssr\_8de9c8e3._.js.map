{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Logo.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LogoProps {\n  className?: string;\n}\n\nexport default function Logo({ className = \"\" }: LogoProps) {\n  return (\n    <div className={`w-10 h-10 bg-logo-bg rounded-lg flex items-center justify-center ${className}`}>\n      <span className=\"text-white font-bold text-xl\">M</span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAMe,SAAS,KAAK,EAAE,YAAY,EAAE,EAAa;IACxD,qBACE,8OAAC;QAAI,WAAW,CAAC,iEAAiE,EAAE,WAAW;kBAC7F,cAAA,8OAAC;YAAK,WAAU;sBAA+B;;;;;;;;;;;AAGrD", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Logo from './Logo';\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-gray-900 text-white py-12\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"md:col-span-2\">\n            <div className=\"flex items-center mb-4\">\n              <Logo />\n              <span className=\"ml-3 text-xl font-bold\">Mobilify</span>\n            </div>\n            <p className=\"text-gray-300 mb-4 max-w-md\">\n              Transforming your concepts and existing websites into stunning, \n              high-performance mobile apps. We are the bridge from vision to launch.\n            </p>\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                <span className=\"sr-only\">Twitter</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                <span className=\"sr-only\">LinkedIn</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                <span className=\"sr-only\">GitHub</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <a href=\"/services\" className=\"text-gray-300 hover:text-white transition-colors duration-200\">\n                  Services\n                </a>\n              </li>\n              <li>\n                <a href=\"/#process\" className=\"text-gray-300 hover:text-white transition-colors duration-200\">\n                  How It Works\n                </a>\n              </li>\n              <li>\n                <a href=\"/about\" className=\"text-gray-300 hover:text-white transition-colors duration-200\">\n                  About Us\n                </a>\n              </li>\n              <li>\n                <a href=\"/#contact\" className=\"text-gray-300 hover:text-white transition-colors duration-200\">\n                  Contact\n                </a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Contact</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><EMAIL></li>\n              <li>Response within 24 hours</li>\n              <li>Free consultation</li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-gray-400 text-sm\">\n            © {currentYear} Mobilify. All rights reserved.\n          </p>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n              Privacy Policy\n            </a>\n            <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n              Terms of Service\n            </a>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAEe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0HAAA,CAAA,UAAI;;;;;sDACL,8OAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAgE;;;;;;;;;;;sDAIhG,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAgE;;;;;;;;;;;sDAIhG,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAS,WAAU;0DAAgE;;;;;;;;;;;sDAI7F,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAgE;;;;;;;;;;;;;;;;;;;;;;;sCAQpG,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;8BAMV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAAwB;gCAChC;gCAAY;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAwE;;;;;;8CAG9F,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1G", "debugId": null}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/AnimatedSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AnimatedSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AnimatedSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 474, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/AnimatedSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AnimatedSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AnimatedSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/StaggeredList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StaggeredList.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StaggeredList.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/StaggeredList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StaggeredList.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StaggeredList.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/app/about/page.tsx"], "sourcesContent": ["import Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport AnimatedSection from '@/components/AnimatedSection';\nimport StaggeredList from '@/components/StaggeredList';\n\nconst teamMembers = [\n  {\n    name: '<PERSON>',\n    role: 'CEO & Visionary',\n    bio: 'Former Product Lead at Shopify with 8+ years of experience in mobile app development. <PERSON> is passionate about helping small businesses succeed and frustrated with the high barriers to mobile app development. He founded Mobilify to democratize app creation.',\n    expertise: ['Product Strategy', 'Business Development', 'User Experience', 'Team Leadership'],\n    image: '/team/alex-chen.jpg' // Placeholder - would use AI-generated image\n  },\n  {\n    name: '<PERSON>',\n    role: 'CTO & Lead Architect',\n    bio: 'Former Senior Staff Engineer at Twilio with expertise in scalable systems and AI. <PERSON> has built developer tools used by millions and brings her vision of AI-democratized development to Mobilify. She leads our technical architecture and innovation.',\n    expertise: ['System Architecture', 'AI/ML', 'Mobile Development', 'DevOps'],\n    image: '/team/maria-garcia.jpg' // Placeholder - would use AI-generated image\n  }\n];\n\nconst values = [\n  {\n    title: 'Quality Craftsmanship',\n    description: 'Every line of code is written with precision and care. We believe in building apps that not only work flawlessly but also delight users.',\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n      </svg>\n    )\n  },\n  {\n    title: 'Client Partnership',\n    description: 'We work alongside you as true partners in success. Your goals become our goals, and we\\'re not satisfied until you achieve them.',\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n      </svg>\n    )\n  },\n  {\n    title: 'Transparency',\n    description: 'Clear communication and honest timelines, always. We believe in keeping you informed every step of the way.',\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n      </svg>\n    )\n  },\n  {\n    title: 'Innovation',\n    description: 'We stay at the forefront of technology to bring you the latest and most effective solutions for your mobile app needs.',\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n      </svg>\n    )\n  }\n];\n\nexport default function AboutPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header />\n      \n      <main className=\"pt-16\">\n        {/* Hero Section */}\n        <section className=\"py-20 bg-gradient-to-br from-gray-50 to-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <AnimatedSection className=\"text-center\">\n              <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight mb-6\">\n                About <span className=\"text-primary\">Mobilify</span>\n              </h1>\n              <p className=\"text-xl text-gray-600 max-w-3xl mx-auto mb-8\">\n                We're more than just developers. We're passionate about helping founders, \n                entrepreneurs, and businesses bridge the gap between vision and execution.\n              </p>\n            </AnimatedSection>\n          </div>\n        </section>\n\n        {/* Mission Section */}\n        <section className=\"py-20\">\n          <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <AnimatedSection className=\"bg-white rounded-3xl p-8 md:p-12 shadow-xl\">\n              <h2 className=\"text-3xl font-bold text-foreground mb-8 text-center\">Our Mission</h2>\n              <div className=\"space-y-6 text-lg text-gray-700 leading-relaxed\">\n                <p>\n                  At Mobilify, we believe that every great idea deserves to become a reality. \n                  We're passionate about helping founders, entrepreneurs, and businesses bridge \n                  the gap between vision and execution.\n                </p>\n                <p>\n                  Our commitment goes beyond just writing code. We partner with you to understand \n                  your goals, challenges, and dreams. We're not satisfied until your app not only \n                  works flawlessly but also delights your users and drives your business forward.\n                </p>\n                <p>\n                  Founded in 2024, Mobilify was born from the frustration of seeing great ideas \n                  never make it to market due to technical barriers and high development costs. \n                  We set out to change that by making professional mobile app development \n                  accessible to everyone.\n                </p>\n              </div>\n            </AnimatedSection>\n          </div>\n        </section>\n\n        {/* Values Section */}\n        <section className=\"py-20 bg-gray-50\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <AnimatedSection className=\"text-center mb-16\">\n              <h2 className=\"text-4xl font-bold text-foreground mb-6\">\n                Our <span className=\"text-primary\">Values</span>\n              </h2>\n              <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n                These principles guide everything we do and shape how we work with our clients.\n              </p>\n            </AnimatedSection>\n\n            <StaggeredList className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              {values.map((value, index) => (\n                <div key={index} className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group\">\n                  <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-6 group-hover:bg-primary/20 transition-colors duration-300\">\n                    <div className=\"text-primary\">\n                      {value.icon}\n                    </div>\n                  </div>\n                  <h3 className=\"text-xl font-bold text-foreground mb-4\">{value.title}</h3>\n                  <p className=\"text-gray-600 leading-relaxed\">{value.description}</p>\n                </div>\n              ))}\n            </StaggeredList>\n          </div>\n        </section>\n\n        {/* Team Section */}\n        <section className=\"py-20\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <AnimatedSection className=\"text-center mb-16\">\n              <h2 className=\"text-4xl font-bold text-foreground mb-6\">\n                Meet Our <span className=\"text-primary\">Team</span>\n              </h2>\n              <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n                The passionate individuals behind Mobilify who are dedicated to bringing your ideas to life.\n              </p>\n            </AnimatedSection>\n\n            <StaggeredList className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n              {teamMembers.map((member, index) => (\n                <div key={index} className=\"bg-white rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300\">\n                  <div className=\"flex flex-col items-center text-center\">\n                    {/* Placeholder for team member photo */}\n                    <div className=\"w-32 h-32 bg-gray-200 rounded-full mb-6 flex items-center justify-center\">\n                      <span className=\"text-4xl font-bold text-gray-400\">\n                        {member.name.split(' ').map(n => n[0]).join('')}\n                      </span>\n                    </div>\n                    \n                    <h3 className=\"text-2xl font-bold text-foreground mb-2\">{member.name}</h3>\n                    <p className=\"text-primary font-semibold mb-4\">{member.role}</p>\n                    <p className=\"text-gray-600 leading-relaxed mb-6\">{member.bio}</p>\n                    \n                    <div className=\"w-full\">\n                      <h4 className=\"font-semibold text-foreground mb-3\">Expertise:</h4>\n                      <div className=\"flex flex-wrap gap-2 justify-center\">\n                        {member.expertise.map((skill, skillIndex) => (\n                          <span\n                            key={skillIndex}\n                            className=\"bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium\"\n                          >\n                            {skill}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </StaggeredList>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-20 bg-gray-50\">\n          <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <AnimatedSection>\n              <h2 className=\"text-4xl font-bold text-foreground mb-6\">\n                Ready to Work Together?\n              </h2>\n              <p className=\"text-xl text-gray-600 mb-8\">\n                Let's discuss your project and see how we can help bring your vision to life.\n              </p>\n              <a\n                href=\"/#contact\"\n                className=\"bg-primary text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary/90 transition-all duration-300 transform hover:scale-105 inline-block\"\n              >\n                Get in Touch\n              </a>\n            </AnimatedSection>\n          </div>\n        </section>\n      </main>\n      \n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,WAAW;YAAC;YAAoB;YAAwB;YAAmB;SAAkB;QAC7F,OAAO,sBAAsB,6CAA6C;IAC5E;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,WAAW;YAAC;YAAuB;YAAS;YAAsB;SAAS;QAC3E,OAAO,yBAAyB,6CAA6C;IAC/E;CACD;AAED,MAAM,SAAS;IACb;QACE,OAAO;QACP,aAAa;QACb,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,OAAO;QACP,aAAa;QACb,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,OAAO;QACP,aAAa;QACb,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;;8BACjE,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;8BACrE,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;;IAG3E;IACA;QACE,OAAO;QACP,aAAa;QACb,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qIAAA,CAAA,UAAe;gCAAC,WAAU;;kDACzB,8OAAC;wCAAG,WAAU;;4CAAgF;0DACtF,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAEvC,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;;;;;;;;;;;;;;;;;kCASlE,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qIAAA,CAAA,UAAe;gCAAC,WAAU;;kDACzB,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAE;;;;;;0DAKH,8OAAC;0DAAE;;;;;;0DAKH,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYX,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qIAAA,CAAA,UAAe;oCAAC,WAAU;;sDACzB,8OAAC;4CAAG,WAAU;;gDAA0C;8DAClD,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAErC,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAKzD,8OAAC,mIAAA,CAAA,UAAa;oCAAC,WAAU;8CACtB,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,MAAM,IAAI;;;;;;;;;;;8DAGf,8OAAC;oDAAG,WAAU;8DAA0C,MAAM,KAAK;;;;;;8DACnE,8OAAC;oDAAE,WAAU;8DAAiC,MAAM,WAAW;;;;;;;2CAPvD;;;;;;;;;;;;;;;;;;;;;kCAelB,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qIAAA,CAAA,UAAe;oCAAC,WAAU;;sDACzB,8OAAC;4CAAG,WAAU;;gDAA0C;8DAC7C,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAE1C,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAKzD,8OAAC,mIAAA,CAAA,UAAa;oCAAC,WAAU;8CACtB,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;4CAAgB,WAAU;sDACzB,cAAA,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEACb,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;kEAIhD,8OAAC;wDAAG,WAAU;kEAA2C,OAAO,IAAI;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAAmC,OAAO,IAAI;;;;;;kEAC3D,8OAAC;wDAAE,WAAU;kEAAsC,OAAO,GAAG;;;;;;kEAE7D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAqC;;;;;;0EACnD,8OAAC;gEAAI,WAAU;0EACZ,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC5B,8OAAC;wEAEC,WAAU;kFAET;uEAHI;;;;;;;;;;;;;;;;;;;;;;2CAlBP;;;;;;;;;;;;;;;;;;;;;kCAkClB,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qIAAA,CAAA,UAAe;;kDACd,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDAGxD,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 1149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,MAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}