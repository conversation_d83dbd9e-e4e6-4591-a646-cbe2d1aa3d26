'use client';

import React from 'react';

interface StaggeredListProps {
  children: React.ReactNode[];
  className?: string;
  staggerDelay?: number;
}

export default function StaggeredList({
  children,
  className = '',
  staggerDelay = 0.1
}: StaggeredListProps) {
  // Temporary: Return a simple div without animations
  // TODO: Add framer-motion staggered animations later
  return (
    <div className={className}>
      {children.map((child, index) => (
        <div key={index}>
          {child}
        </div>
      ))}
    </div>
  );
}
