import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AnimatedSection from '@/components/AnimatedSection';
import StaggeredList from '@/components/StaggeredList';

const services = [
  {
    title: 'Starter App',
    price: '$5,000',
    timeline: '2-3 weeks',
    description: 'Perfect for converting existing websites into mobile apps',
    features: [
      'Website to app conversion',
      'Basic customization',
      'iOS & Android apps',
      'App store submission',
      'Basic analytics integration',
      '30 days support',
      'Source code included'
    ],
    popular: false,
    color: 'blue'
  },
  {
    title: 'Custom App',
    price: '$15,000',
    timeline: '6-8 weeks',
    description: 'Turn your innovative ideas into reality with custom development',
    features: [
      'Custom app development',
      'Advanced features & integrations',
      'UI/UX design included',
      'Backend development',
      'API integrations',
      'App store optimization',
      'Advanced analytics',
      'Push notifications',
      '90 days support',
      'Source code included'
    ],
    popular: true,
    color: 'primary'
  },
  {
    title: 'Enterprise',
    price: 'Contact Us',
    timeline: '10-16 weeks',
    description: 'Bespoke solutions for complex projects and large organizations',
    features: [
      'Complex integrations',
      'Scalable architecture',
      'Dedicated development team',
      'Custom backend solutions',
      'Advanced security features',
      'Multi-platform deployment',
      'Ongoing maintenance',
      'Priority support',
      'Training & documentation',
      'Source code included'
    ],
    popular: false,
    color: 'purple'
  }
];

const addOns = [
  {
    name: 'Additional Platform',
    description: 'Add web app or additional mobile platform',
    price: '+$2,000'
  },
  {
    name: 'Advanced Analytics',
    description: 'Custom analytics dashboard and reporting',
    price: '+$1,500'
  },
  {
    name: 'Payment Integration',
    description: 'Stripe, PayPal, or custom payment gateway',
    price: '+$1,000'
  },
  {
    name: 'Admin Panel',
    description: 'Web-based content management system',
    price: '+$3,000'
  }
];

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="pt-16">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <AnimatedSection className="text-center">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight mb-6">
                Our <span className="text-primary">Services</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                From simple website conversions to complex enterprise solutions, 
                we have the perfect package for your mobile app needs.
              </p>
            </AnimatedSection>
          </div>
        </section>

        {/* Detailed Service Cards */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <StaggeredList className="space-y-16">
              {services.map((service, index) => (
                <div
                  key={index}
                  className={`bg-white rounded-3xl p-8 md:p-12 shadow-xl border-2 ${
                    service.popular ? 'border-primary' : 'border-gray-100'
                  }`}
                >
                  {service.popular && (
                    <div className="text-center mb-6">
                      <span className="bg-primary text-white px-6 py-2 rounded-full text-sm font-semibold">
                        Most Popular
                      </span>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Service Info */}
                    <div className="lg:col-span-1">
                      <h3 className="text-3xl font-bold text-foreground mb-4">{service.title}</h3>
                      <div className="text-4xl font-bold text-primary mb-2">{service.price}</div>
                      <div className="text-gray-600 mb-4">Timeline: {service.timeline}</div>
                      <p className="text-gray-700 mb-6">{service.description}</p>
                      <button className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 ${
                        service.popular
                          ? 'bg-primary text-white hover:bg-primary/90'
                          : 'bg-gray-100 text-gray-800 hover:bg-primary hover:text-white'
                      }`}>
                        Get Started
                      </button>
                    </div>

                    {/* Features */}
                    <div className="lg:col-span-2">
                      <h4 className="text-xl font-semibold text-foreground mb-6">What's Included:</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {service.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center">
                            <svg className="w-5 h-5 text-primary mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            <span className="text-gray-700">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </StaggeredList>
          </div>
        </section>

        {/* Add-ons Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <AnimatedSection className="text-center mb-16">
              <h2 className="text-4xl font-bold text-foreground mb-6">
                Optional <span className="text-primary">Add-ons</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Enhance your app with additional features and integrations.
              </p>
            </AnimatedSection>

            <StaggeredList className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {addOns.map((addon, index) => (
                <div key={index} className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-xl font-semibold text-foreground">{addon.name}</h3>
                    <span className="text-primary font-bold">{addon.price}</span>
                  </div>
                  <p className="text-gray-600">{addon.description}</p>
                </div>
              ))}
            </StaggeredList>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AnimatedSection>
              <h2 className="text-4xl font-bold text-foreground mb-6">
                Ready to Get Started?
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Let's discuss your project and find the perfect solution for your needs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/#contact"
                  className="bg-primary text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary/90 transition-all duration-300 transform hover:scale-105"
                >
                  Get Free Quote
                </a>
                <a
                  href="/#demo"
                  className="bg-gray-100 text-gray-800 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-200 transition-all duration-300"
                >
                  Try Demo
                </a>
              </div>
            </AnimatedSection>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
