'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import AnimatedSection from './AnimatedSection';

export default function InteractiveDemo() {
  const [activeTab, setActiveTab] = useState<'website' | 'idea'>('website');
  const [inputValue, setInputValue] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showResult, setShowResult] = useState(false);

  const handleGenerate = () => {
    if (!inputValue.trim()) return;

    setIsGenerating(true);
    setShowResult(false);

    // Simulate generation process
    setTimeout(() => {
      setIsGenerating(false);
      setShowResult(true);
    }, 3000);
  };

  return (
    <section id="demo" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <AnimatedSection className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold text-foreground mb-6">
            From Zero to App, <span className="text-primary">Instantly</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience the power of our platform. Enter a website URL or describe your idea,
            and watch as we generate a beautiful mobile app preview.
          </p>
        </AnimatedSection>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Interactive Tool */}
          <div className="space-y-6">
            {/* Tabs */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('website')}
                className={`flex-1 py-3 px-4 rounded-md font-semibold transition-all duration-200 ${
                  activeTab === 'website'
                    ? 'bg-white text-primary shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Convert a Website
              </button>
              <button
                onClick={() => setActiveTab('idea')}
                className={`flex-1 py-3 px-4 rounded-md font-semibold transition-all duration-200 ${
                  activeTab === 'idea'
                    ? 'bg-white text-primary shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Describe an Idea
              </button>
            </div>

            {/* Input Field */}
            <div className="space-y-4">
              <label className="block text-sm font-medium text-gray-700">
                {activeTab === 'website' ? 'Website URL' : 'Your App Idea'}
              </label>
              <input
                type={activeTab === 'website' ? 'url' : 'text'}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={
                  activeTab === 'website'
                    ? 'https://your-website.com'
                    : 'Describe your app idea in a few words...'
                }
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
              />
            </div>

            {/* Generate Button */}
            <button
              onClick={handleGenerate}
              disabled={!inputValue.trim() || isGenerating}
              className="w-full bg-primary text-white py-4 rounded-lg font-semibold text-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105"
            >
              {isGenerating ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Generating...
                </span>
              ) : (
                'Mobilify Preview'
              )}
            </button>
          </div>

          {/* Right Column - Phone Mockup */}
          <div className="flex justify-center">
            <div className="relative">
              {/* Phone Frame */}
              <div className="w-72 h-96 bg-gray-900 rounded-3xl p-3 shadow-2xl transform perspective-1000 rotate-y-12">
                <div className="w-full h-full bg-white rounded-2xl overflow-hidden relative">
                  {isGenerating ? (
                    // Loading State
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                        <p className="text-gray-600">Creating your app...</p>
                      </div>
                    </div>
                  ) : (
                    // Default State
                    <div className="p-6 h-full flex flex-col">
                      <div className="text-center mb-6">
                        <div className="w-12 h-12 bg-gray-200 rounded-full mx-auto mb-3"></div>
                        <h3 className="font-semibold text-gray-800">Your App Preview</h3>
                        <p className="text-sm text-gray-500">Will appear here</p>
                      </div>
                      
                      {/* Placeholder UI Elements */}
                      <div className="space-y-4 flex-1">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                        <div className="h-20 bg-gray-100 rounded"></div>
                        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                      </div>
                      
                      {/* Bottom Navigation Placeholder */}
                      <div className="flex justify-around pt-4 border-t border-gray-200">
                        <div className="w-6 h-6 bg-gray-300 rounded"></div>
                        <div className="w-6 h-6 bg-gray-300 rounded"></div>
                        <div className="w-6 h-6 bg-primary rounded"></div>
                        <div className="w-6 h-6 bg-gray-300 rounded"></div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
