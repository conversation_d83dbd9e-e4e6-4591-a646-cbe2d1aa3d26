'use client';

import React from 'react';
import { motion } from 'framer-motion';
import AnimatedSection from './AnimatedSection';

export default function Hero() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="hero" className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <AnimatedSection className="text-center lg:text-left">
            <motion.h1
              className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight mb-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Your Idea.{' '}
              <motion.span
                className="text-primary"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                Your App.
              </motion.span>{' '}
              Realized.
            </motion.h1>

            <motion.p
              className="text-xl text-gray-600 mb-8 leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Mobilify transforms your concepts and existing websites into stunning,
              high-performance mobile apps. We are the bridge from vision to launch.
            </motion.p>

            <motion.button
              onClick={() => scrollToSection('demo')}
              className="bg-primary text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary/90 transition-all duration-300 shadow-lg hover:shadow-xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              See How It Works
            </motion.button>
          </AnimatedSection>

          {/* Right Column - Visual Placeholder */}
          <AnimatedSection className="flex justify-center lg:justify-end" delay={0.4} direction="right">
            <div className="relative">
              {/* Phone Mockup Placeholder */}
              <motion.div
                className="w-64 h-96 bg-gray-800 rounded-3xl p-2 shadow-2xl"
                initial={{ rotate: 3, scale: 0.9 }}
                animate={{ rotate: 3, scale: 1 }}
                transition={{ duration: 1, delay: 0.8 }}
                whileHover={{ rotate: 0, scale: 1.05 }}
              >
                <div className="w-full h-full bg-white rounded-2xl flex items-center justify-center">
                  <div className="text-center">
                    <motion.div
                      className="w-16 h-16 bg-primary rounded-full mx-auto mb-4 flex items-center justify-center"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.5, delay: 1.2 }}
                    >
                      <span className="text-white font-bold text-2xl">M</span>
                    </motion.div>
                    <motion.p
                      className="text-gray-600 text-sm"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 1.4 }}
                    >
                      App Preview
                    </motion.p>
                    <motion.p
                      className="text-gray-400 text-xs mt-2"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 1.6 }}
                    >
                      Interactive Demo Below
                    </motion.p>
                  </div>
                </div>
              </motion.div>

              {/* Floating Elements */}
              <motion.div
                className="absolute -top-4 -left-4 w-8 h-8 bg-primary rounded-full opacity-20"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.2, 0.4, 0.2]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: 2
                }}
              />
              <motion.div
                className="absolute -bottom-4 -right-4 w-6 h-6 bg-primary rounded-full opacity-30"
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.3, 0.5, 0.3]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: 2.5
                }}
              />
            </div>
          </AnimatedSection>
        </div>
      </div>
    </section>
  );
}
