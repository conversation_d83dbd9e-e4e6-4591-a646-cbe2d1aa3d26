'use client';

import React from 'react';
import AnimatedSection from './AnimatedSection';

export default function Hero() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="hero" className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <AnimatedSection className="text-center lg:text-left">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight mb-6">
              Your Idea.{' '}
              <span className="text-primary">Your App.</span>{' '}
              Realized.
            </h1>

            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Mobilify transforms your concepts and existing websites into stunning,
              high-performance mobile apps. We are the bridge from vision to launch.
            </p>

            <button
              onClick={() => scrollToSection('demo')}
              className="bg-primary text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary/90 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              See How It Works
            </button>
          </AnimatedSection>

          {/* Right Column - Visual Placeholder */}
          <AnimatedSection className="flex justify-center lg:justify-end" delay={0.4} direction="right">
            <div className="relative">
              {/* Phone Mockup Placeholder */}
              <div className="w-64 h-96 bg-gray-800 rounded-3xl p-2 shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500">
                <div className="w-full h-full bg-white rounded-2xl flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-primary rounded-full mx-auto mb-4 flex items-center justify-center">
                      <span className="text-white font-bold text-2xl">M</span>
                    </div>
                    <p className="text-gray-600 text-sm">App Preview</p>
                    <p className="text-gray-400 text-xs mt-2">Interactive Demo Below</p>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute -bottom-4 -right-4 w-6 h-6 bg-primary rounded-full opacity-30 animate-pulse delay-300"></div>
            </div>
          </AnimatedSection>
        </div>
      </div>
    </section>
  );
}
