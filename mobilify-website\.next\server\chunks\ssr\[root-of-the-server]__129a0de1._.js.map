{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Logo.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LogoProps {\n  className?: string;\n}\n\nexport default function Logo({ className = \"\" }: LogoProps) {\n  return (\n    <div className={`w-10 h-10 bg-logo-bg rounded-lg flex items-center justify-center ${className}`}>\n      <span className=\"text-white font-bold text-xl\">M</span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAMe,SAAS,KAAK,EAAE,YAAY,EAAE,EAAa;IACxD,qBACE,8OAAC;QAAI,WAAW,CAAC,iEAAiE,EAAE,WAAW;kBAC7F,cAAA,8OAAC;YAAK,WAAU;sBAA+B;;;;;;;;;;;AAGrD", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Logo from './Logo';\n\nexport default function Header() {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    setIsMobileMenuOpen(false); // Close mobile menu after navigation\n  };\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-b border-gray-100 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Logo />\n            <span className=\"ml-3 text-xl font-bold text-foreground\">Mobilify</span>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <button\n              onClick={() => scrollToSection('services-overview')}\n              className=\"text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              Services\n            </button>\n            <button\n              onClick={() => scrollToSection('process')}\n              className=\"text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              How It Works\n            </button>\n            <button\n              onClick={() => scrollToSection('about')}\n              className=\"text-foreground hover:text-primary transition-colors duration-200\"\n            >\n              About Us\n            </button>\n          </nav>\n\n          {/* Desktop CTA Button */}\n          <button\n            onClick={() => scrollToSection('contact')}\n            className=\"hidden md:block bg-primary text-white px-6 py-2 rounded-lg font-semibold hover:bg-primary/90 transition-colors duration-200\"\n          >\n            Get a Quote\n          </button>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\"\n          >\n            <motion.div\n              animate={isMobileMenuOpen ? \"open\" : \"closed\"}\n              className=\"w-6 h-6 flex flex-col justify-center items-center\"\n            >\n              <motion.span\n                variants={{\n                  closed: { rotate: 0, y: 0 },\n                  open: { rotate: 45, y: 6 }\n                }}\n                className=\"w-6 h-0.5 bg-foreground block transition-all duration-300 origin-center\"\n              />\n              <motion.span\n                variants={{\n                  closed: { opacity: 1 },\n                  open: { opacity: 0 }\n                }}\n                className=\"w-6 h-0.5 bg-foreground block mt-1.5 transition-all duration-300\"\n              />\n              <motion.span\n                variants={{\n                  closed: { rotate: 0, y: 0 },\n                  open: { rotate: -45, y: -6 }\n                }}\n                className=\"w-6 h-0.5 bg-foreground block mt-1.5 transition-all duration-300 origin-center\"\n              />\n            </motion.div>\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden bg-white border-t border-gray-100 shadow-lg\"\n          >\n            <div className=\"max-w-7xl mx-auto px-4 py-6 space-y-4\">\n              <button\n                onClick={() => scrollToSection('services-overview')}\n                className=\"block w-full text-left py-3 text-foreground hover:text-primary transition-colors duration-200\"\n              >\n                Services\n              </button>\n              <button\n                onClick={() => scrollToSection('process')}\n                className=\"block w-full text-left py-3 text-foreground hover:text-primary transition-colors duration-200\"\n              >\n                How It Works\n              </button>\n              <button\n                onClick={() => scrollToSection('about')}\n                className=\"block w-full text-left py-3 text-foreground hover:text-primary transition-colors duration-200\"\n              >\n                About Us\n              </button>\n              <button\n                onClick={() => scrollToSection('contact')}\n                className=\"w-full bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors duration-200 mt-4\"\n              >\n                Get a Quote\n              </button>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;AAEA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,oBAAoB,QAAQ,qCAAqC;IACnE;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,UAAI;;;;;8CACL,8OAAC;oCAAK,WAAU;8CAAyC;;;;;;;;;;;;sCAI3D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAU;sCACX;;;;;;sCAKD,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;sCAEV,cAAA,8OAAC,OAAO,GAAG;gCACT,SAAS,mBAAmB,SAAS;gCACrC,WAAU;;kDAEV,8OAAC,OAAO,IAAI;wCACV,UAAU;4CACR,QAAQ;gDAAE,QAAQ;gDAAG,GAAG;4CAAE;4CAC1B,MAAM;gDAAE,QAAQ;gDAAI,GAAG;4CAAE;wCAC3B;wCACA,WAAU;;;;;;kDAEZ,8OAAC,OAAO,IAAI;wCACV,UAAU;4CACR,QAAQ;gDAAE,SAAS;4CAAE;4CACrB,MAAM;gDAAE,SAAS;4CAAE;wCACrB;wCACA,WAAU;;;;;;kDAEZ,8OAAC,OAAO,IAAI;wCACV,UAAU;4CACR,QAAQ;gDAAE,QAAQ;gDAAG,GAAG;4CAAE;4CAC1B,MAAM;gDAAE,QAAQ,CAAC;gDAAI,GAAG,CAAC;4CAAE;wCAC7B;wCACA,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,8OAAC;0BACE,kCACC,8OAAC,OAAO,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/AnimatedSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface AnimatedSectionProps {\n  children: React.ReactNode;\n  className?: string;\n  delay?: number;\n  direction?: 'up' | 'down' | 'left' | 'right';\n}\n\nexport default function AnimatedSection({ \n  children, \n  className = '', \n  delay = 0,\n  direction = 'up'\n}: AnimatedSectionProps) {\n  const getInitialPosition = () => {\n    switch (direction) {\n      case 'up':\n        return { y: 50, opacity: 0 };\n      case 'down':\n        return { y: -50, opacity: 0 };\n      case 'left':\n        return { x: -50, opacity: 0 };\n      case 'right':\n        return { x: 50, opacity: 0 };\n      default:\n        return { y: 50, opacity: 0 };\n    }\n  };\n\n  const getFinalPosition = () => {\n    return { x: 0, y: 0, opacity: 1 };\n  };\n\n  return (\n    <motion.div\n      className={className}\n      initial={getInitialPosition()}\n      whileInView={getFinalPosition()}\n      viewport={{ once: true, margin: \"-100px\" }}\n      transition={{\n        duration: 0.6,\n        delay,\n        ease: [0.25, 0.25, 0.25, 0.75]\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAYe,SAAS,gBAAgB,EACtC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,CAAC,EACT,YAAY,IAAI,EACK;IACrB,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,GAAG;oBAAI,SAAS;gBAAE;YAC7B,KAAK;gBACH,OAAO;oBAAE,GAAG,CAAC;oBAAI,SAAS;gBAAE;YAC9B,KAAK;gBACH,OAAO;oBAAE,GAAG,CAAC;oBAAI,SAAS;gBAAE;YAC9B,KAAK;gBACH,OAAO;oBAAE,GAAG;oBAAI,SAAS;gBAAE;YAC7B;gBACE,OAAO;oBAAE,GAAG;oBAAI,SAAS;gBAAE;QAC/B;IACF;IAEA,MAAM,mBAAmB;QACvB,OAAO;YAAE,GAAG;YAAG,GAAG;YAAG,SAAS;QAAE;IAClC;IAEA,qBACE,8OAAC,OAAO,GAAG;QACT,WAAW;QACX,SAAS;QACT,aAAa;QACb,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;QACzC,YAAY;YACV,UAAU;YACV;YACA,MAAM;gBAAC;gBAAM;gBAAM;gBAAM;aAAK;QAChC;kBAEC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport AnimatedSection from './AnimatedSection';\n\nexport default function Hero() {\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"hero\" className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Column - Content */}\n          <AnimatedSection className=\"text-center lg:text-left\">\n            <motion.h1\n              className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight mb-6\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n            >\n              Your Idea.{' '}\n              <motion.span\n                className=\"text-primary\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ duration: 0.8, delay: 0.6 }}\n              >\n                Your App.\n              </motion.span>{' '}\n              Realized.\n            </motion.h1>\n\n            <motion.p\n              className=\"text-xl text-gray-600 mb-8 leading-relaxed\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n            >\n              Mobilify transforms your concepts and existing websites into stunning,\n              high-performance mobile apps. We are the bridge from vision to launch.\n            </motion.p>\n\n            <motion.button\n              onClick={() => scrollToSection('demo')}\n              className=\"bg-primary text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary/90 transition-all duration-300 shadow-lg hover:shadow-xl\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              See How It Works\n            </motion.button>\n          </AnimatedSection>\n\n          {/* Right Column - Visual Placeholder */}\n          <AnimatedSection className=\"flex justify-center lg:justify-end\" delay={0.4} direction=\"right\">\n            <div className=\"relative\">\n              {/* Phone Mockup Placeholder */}\n              <motion.div\n                className=\"w-64 h-96 bg-gray-800 rounded-3xl p-2 shadow-2xl\"\n                initial={{ rotate: 3, scale: 0.9 }}\n                animate={{ rotate: 3, scale: 1 }}\n                transition={{ duration: 1, delay: 0.8 }}\n                whileHover={{ rotate: 0, scale: 1.05 }}\n              >\n                <div className=\"w-full h-full bg-white rounded-2xl flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <motion.div\n                      className=\"w-16 h-16 bg-primary rounded-full mx-auto mb-4 flex items-center justify-center\"\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      transition={{ duration: 0.5, delay: 1.2 }}\n                    >\n                      <span className=\"text-white font-bold text-2xl\">M</span>\n                    </motion.div>\n                    <motion.p\n                      className=\"text-gray-600 text-sm\"\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      transition={{ delay: 1.4 }}\n                    >\n                      App Preview\n                    </motion.p>\n                    <motion.p\n                      className=\"text-gray-400 text-xs mt-2\"\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      transition={{ delay: 1.6 }}\n                    >\n                      Interactive Demo Below\n                    </motion.p>\n                  </div>\n                </div>\n              </motion.div>\n\n              {/* Floating Elements */}\n              <motion.div\n                className=\"absolute -top-4 -left-4 w-8 h-8 bg-primary rounded-full opacity-20\"\n                animate={{\n                  scale: [1, 1.2, 1],\n                  opacity: [0.2, 0.4, 0.2]\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  delay: 2\n                }}\n              />\n              <motion.div\n                className=\"absolute -bottom-4 -right-4 w-6 h-6 bg-primary rounded-full opacity-30\"\n                animate={{\n                  scale: [1, 1.3, 1],\n                  opacity: [0.3, 0.5, 0.3]\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  delay: 2.5\n                }}\n              />\n            </div>\n          </AnimatedSection>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAIA;AAJA;;;;AAMe,SAAS;IACtB,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,qIAAA,CAAA,UAAe;wBAAC,WAAU;;0CACzB,8OAAC,OAAO,EAAE;gCACR,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;oCACzC;oCACY;kDACX,8OAAC,OAAO,IAAI;wCACV,WAAU;wCACV,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;kDACzC;;;;;;oCAEc;oCAAI;;;;;;;0CAIrB,8OAAC,OAAO,CAAC;gCACP,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CACzC;;;;;;0CAKD,8OAAC,OAAO,MAAM;gCACZ,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CACzB;;;;;;;;;;;;kCAMH,8OAAC,qIAAA,CAAA,UAAe;wBAAC,WAAU;wBAAqC,OAAO;wBAAK,WAAU;kCACpF,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,OAAO,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,QAAQ;wCAAG,OAAO;oCAAI;oCACjC,SAAS;wCAAE,QAAQ;wCAAG,OAAO;oCAAE;oCAC/B,YAAY;wCAAE,UAAU;wCAAG,OAAO;oCAAI;oCACtC,YAAY;wCAAE,QAAQ;wCAAG,OAAO;oCAAK;8CAErC,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,OAAO,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,OAAO;oDAAE;oDACpB,SAAS;wDAAE,OAAO;oDAAE;oDACpB,YAAY;wDAAE,UAAU;wDAAK,OAAO;oDAAI;8DAExC,cAAA,8OAAC;wDAAK,WAAU;kEAAgC;;;;;;;;;;;8DAElD,8OAAC,OAAO,CAAC;oDACP,WAAU;oDACV,SAAS;wDAAE,SAAS;oDAAE;oDACtB,SAAS;wDAAE,SAAS;oDAAE;oDACtB,YAAY;wDAAE,OAAO;oDAAI;8DAC1B;;;;;;8DAGD,8OAAC,OAAO,CAAC;oDACP,WAAU;oDACV,SAAS;wDAAE,SAAS;oDAAE;oDACtB,SAAS;wDAAE,SAAS;oDAAE;oDACtB,YAAY;wDAAE,OAAO;oDAAI;8DAC1B;;;;;;;;;;;;;;;;;;;;;;8CAQP,8OAAC,OAAO,GAAG;oCACT,WAAU;oCACV,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAClB,SAAS;4CAAC;4CAAK;4CAAK;yCAAI;oCAC1B;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO;oCACT;;;;;;8CAEF,8OAAC,OAAO,GAAG;oCACT,WAAU;oCACV,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAClB,SAAS;4CAAC;4CAAK;4CAAK;yCAAI;oCAC1B;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO;oCACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/InteractiveDemo.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport AnimatedSection from './AnimatedSection';\n\nexport default function InteractiveDemo() {\n  const [activeTab, setActiveTab] = useState<'website' | 'idea'>('website');\n  const [inputValue, setInputValue] = useState('');\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [showResult, setShowResult] = useState(false);\n\n  const handleGenerate = () => {\n    if (!inputValue.trim()) return;\n\n    setIsGenerating(true);\n    setShowResult(false);\n\n    // Simulate generation process\n    setTimeout(() => {\n      setIsGenerating(false);\n      setShowResult(true);\n    }, 3000);\n  };\n\n  return (\n    <section id=\"demo\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <AnimatedSection className=\"text-center mb-16\">\n          <h2 className=\"text-4xl sm:text-5xl font-bold text-foreground mb-6\">\n            From Zero to App, <span className=\"text-primary\">Instantly</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Experience the power of our platform. Enter a website URL or describe your idea,\n            and watch as we generate a beautiful mobile app preview.\n          </p>\n        </AnimatedSection>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Column - Interactive Tool */}\n          <AnimatedSection className=\"space-y-6\" direction=\"left\">\n            {/* Tabs */}\n            <div className=\"flex bg-gray-100 rounded-lg p-1\">\n              <button\n                onClick={() => setActiveTab('website')}\n                className={`flex-1 py-3 px-4 rounded-md font-semibold transition-all duration-200 ${\n                  activeTab === 'website'\n                    ? 'bg-white text-primary shadow-sm'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                Convert a Website\n              </button>\n              <button\n                onClick={() => setActiveTab('idea')}\n                className={`flex-1 py-3 px-4 rounded-md font-semibold transition-all duration-200 ${\n                  activeTab === 'idea'\n                    ? 'bg-white text-primary shadow-sm'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                Describe an Idea\n              </button>\n            </div>\n\n            {/* Input Field */}\n            <div className=\"space-y-4\">\n              <label className=\"block text-sm font-medium text-gray-700\">\n                {activeTab === 'website' ? 'Website URL' : 'Your App Idea'}\n              </label>\n              <input\n                type={activeTab === 'website' ? 'url' : 'text'}\n                value={inputValue}\n                onChange={(e) => setInputValue(e.target.value)}\n                placeholder={\n                  activeTab === 'website'\n                    ? 'https://your-website.com'\n                    : 'Describe your app idea in a few words...'\n                }\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200\"\n              />\n            </div>\n\n            {/* Generate Button */}\n            <button\n              onClick={handleGenerate}\n              disabled={!inputValue.trim() || isGenerating}\n              className=\"w-full bg-primary text-white py-4 rounded-lg font-semibold text-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105\"\n            >\n              {isGenerating ? (\n                <span className=\"flex items-center justify-center\">\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  Generating...\n                </span>\n              ) : (\n                'Mobilify Preview'\n              )}\n            </button>\n          </AnimatedSection>\n\n          {/* Right Column - Phone Mockup */}\n          <AnimatedSection className=\"flex justify-center\" direction=\"right\" delay={0.3}>\n            <div className=\"relative\">\n              {/* Phone Frame */}\n              <motion.div\n                className=\"w-72 h-96 bg-gray-900 rounded-3xl p-3 shadow-2xl\"\n                initial={{ rotateY: 15, scale: 0.9 }}\n                animate={{ rotateY: 15, scale: 1 }}\n                whileHover={{ rotateY: 0, scale: 1.02 }}\n                transition={{ duration: 0.5 }}\n              >\n                <div className=\"w-full h-full bg-white rounded-2xl overflow-hidden relative\">\n                  <AnimatePresence mode=\"wait\">\n                    {isGenerating ? (\n                      // Loading State\n                      <motion.div\n                        key=\"loading\"\n                        className=\"flex items-center justify-center h-full\"\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        exit={{ opacity: 0 }}\n                      >\n                        <div className=\"text-center\">\n                          <motion.div\n                            className=\"rounded-full h-12 w-12 border-4 border-primary border-t-transparent mx-auto mb-4\"\n                            animate={{ rotate: 360 }}\n                            transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                          />\n                          <motion.p\n                            className=\"text-gray-600\"\n                            animate={{ opacity: [0.5, 1, 0.5] }}\n                            transition={{ duration: 1.5, repeat: Infinity }}\n                          >\n                            Creating your app...\n                          </motion.p>\n                        </div>\n                      </motion.div>\n                    ) : showResult ? (\n                      // Generated App State\n                      <motion.div\n                        key=\"result\"\n                        className=\"p-6 h-full flex flex-col\"\n                        initial={{ opacity: 0, y: 20 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        transition={{ duration: 0.8 }}\n                      >\n                        {/* App Header */}\n                        <motion.div\n                          className=\"text-center mb-6\"\n                          initial={{ opacity: 0, y: -10 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: 0.2 }}\n                        >\n                          <motion.div\n                            className=\"w-12 h-12 bg-primary rounded-full mx-auto mb-3 flex items-center justify-center\"\n                            initial={{ scale: 0 }}\n                            animate={{ scale: 1 }}\n                            transition={{ delay: 0.4, type: \"spring\" }}\n                          >\n                            <span className=\"text-white font-bold\">M</span>\n                          </motion.div>\n                          <h3 className=\"font-semibold text-gray-800\">Your App</h3>\n                          <p className=\"text-sm text-gray-500\">Generated Successfully!</p>\n                        </motion.div>\n\n                        {/* App Content */}\n                        <motion.div\n                          className=\"space-y-4 flex-1\"\n                          initial={{ opacity: 0 }}\n                          animate={{ opacity: 1 }}\n                          transition={{ delay: 0.6 }}\n                        >\n                          <motion.div\n                            className=\"h-4 bg-primary/20 rounded w-3/4\"\n                            initial={{ width: 0 }}\n                            animate={{ width: \"75%\" }}\n                            transition={{ delay: 0.8, duration: 0.5 }}\n                          />\n                          <motion.div\n                            className=\"h-4 bg-primary/20 rounded w-1/2\"\n                            initial={{ width: 0 }}\n                            animate={{ width: \"50%\" }}\n                            transition={{ delay: 1, duration: 0.5 }}\n                          />\n                          <motion.div\n                            className=\"h-20 bg-primary/10 rounded\"\n                            initial={{ height: 0 }}\n                            animate={{ height: \"5rem\" }}\n                            transition={{ delay: 1.2, duration: 0.5 }}\n                          />\n                          <motion.div\n                            className=\"h-4 bg-primary/20 rounded w-2/3\"\n                            initial={{ width: 0 }}\n                            animate={{ width: \"66.666667%\" }}\n                            transition={{ delay: 1.4, duration: 0.5 }}\n                          />\n                        </motion.div>\n\n                        {/* Bottom Navigation */}\n                        <motion.div\n                          className=\"flex justify-around pt-4 border-t border-gray-200\"\n                          initial={{ opacity: 0, y: 10 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: 1.6 }}\n                        >\n                          {[0, 1, 2, 3].map((index) => (\n                            <motion.div\n                              key={index}\n                              className={`w-6 h-6 rounded ${index === 2 ? 'bg-primary' : 'bg-gray-300'}`}\n                              initial={{ scale: 0 }}\n                              animate={{ scale: 1 }}\n                              transition={{ delay: 1.8 + index * 0.1, type: \"spring\" }}\n                            />\n                          ))}\n                        </motion.div>\n                      </motion.div>\n                    ) : (\n                      // Default State\n                      <motion.div\n                        key=\"default\"\n                        className=\"p-6 h-full flex flex-col\"\n                        initial={{ opacity: 1 }}\n                        animate={{ opacity: 1 }}\n                      >\n                        <div className=\"text-center mb-6\">\n                          <div className=\"w-12 h-12 bg-gray-200 rounded-full mx-auto mb-3\"></div>\n                          <h3 className=\"font-semibold text-gray-800\">Your App Preview</h3>\n                          <p className=\"text-sm text-gray-500\">Will appear here</p>\n                        </div>\n\n                        {/* Placeholder UI Elements */}\n                        <div className=\"space-y-4 flex-1\">\n                          <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                          <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n                          <div className=\"h-20 bg-gray-100 rounded\"></div>\n                          <div className=\"h-4 bg-gray-200 rounded w-2/3\"></div>\n                        </div>\n\n                        {/* Bottom Navigation Placeholder */}\n                        <div className=\"flex justify-around pt-4 border-t border-gray-200\">\n                          <div className=\"w-6 h-6 bg-gray-300 rounded\"></div>\n                          <div className=\"w-6 h-6 bg-gray-300 rounded\"></div>\n                          <div className=\"w-6 h-6 bg-primary rounded\"></div>\n                          <div className=\"w-6 h-6 bg-gray-300 rounded\"></div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n                </div>\n              </motion.div>\n            </div>\n          </AnimatedSection>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;AAEA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,iBAAiB;QACrB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,gBAAgB;QAChB,cAAc;QAEd,8BAA8B;QAC9B,WAAW;YACT,gBAAgB;YAChB,cAAc;QAChB,GAAG;IACL;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,qIAAA,CAAA,UAAe;oBAAC,WAAU;;sCACzB,8OAAC;4BAAG,WAAU;;gCAAsD;8CAChD,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAEnD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,qIAAA,CAAA,UAAe;4BAAC,WAAU;4BAAY,WAAU;;8CAE/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,sEAAsE,EAChF,cAAc,YACV,oCACA,qCACJ;sDACH;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,sEAAsE,EAChF,cAAc,SACV,oCACA,qCACJ;sDACH;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDACd,cAAc,YAAY,gBAAgB;;;;;;sDAE7C,8OAAC;4CACC,MAAM,cAAc,YAAY,QAAQ;4CACxC,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,aACE,cAAc,YACV,6BACA;4CAEN,WAAU;;;;;;;;;;;;8CAKd,8OAAC;oCACC,SAAS;oCACT,UAAU,CAAC,WAAW,IAAI,MAAM;oCAChC,WAAU;8CAET,6BACC,8OAAC;wCAAK,WAAU;;0DACd,8OAAC;gDAAI,WAAU;gDAA6C,OAAM;gDAA6B,MAAK;gDAAO,SAAQ;;kEACjH,8OAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,8OAAC;wDAAK,WAAU;wDAAa,MAAK;wDAAe,GAAE;;;;;;;;;;;;4CAC/C;;;;;;+CAIR;;;;;;;;;;;;sCAMN,8OAAC,qIAAA,CAAA,UAAe;4BAAC,WAAU;4BAAsB,WAAU;4BAAQ,OAAO;sCACxE,cAAA,8OAAC;gCAAI,WAAU;0CAEb,cAAA,8OAAC,OAAO,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAI,OAAO;oCAAI;oCACnC,SAAS;wCAAE,SAAS;wCAAI,OAAO;oCAAE;oCACjC,YAAY;wCAAE,SAAS;wCAAG,OAAO;oCAAK;oCACtC,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAgB,MAAK;sDACnB,eACC,gBAAgB;0DAChB,8OAAC,OAAO,GAAG;gDAET,WAAU;gDACV,SAAS;oDAAE,SAAS;gDAAE;gDACtB,SAAS;oDAAE,SAAS;gDAAE;gDACtB,MAAM;oDAAE,SAAS;gDAAE;0DAEnB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,OAAO,GAAG;4DACT,WAAU;4DACV,SAAS;gEAAE,QAAQ;4DAAI;4DACvB,YAAY;gEAAE,UAAU;gEAAG,QAAQ;gEAAU,MAAM;4DAAS;;;;;;sEAE9D,8OAAC,OAAO,CAAC;4DACP,WAAU;4DACV,SAAS;gEAAE,SAAS;oEAAC;oEAAK;oEAAG;iEAAI;4DAAC;4DAClC,YAAY;gEAAE,UAAU;gEAAK,QAAQ;4DAAS;sEAC/C;;;;;;;;;;;;+CAhBC;;;;uDAqBJ,aACF,sBAAsB;0DACtB,8OAAC,OAAO,GAAG;gDAET,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;gDAAI;;kEAG5B,8OAAC,OAAO,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,SAAS;4DAAG,GAAG,CAAC;wDAAG;wDAC9B,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAC5B,YAAY;4DAAE,OAAO;wDAAI;;0EAEzB,8OAAC,OAAO,GAAG;gEACT,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAE;gEACpB,YAAY;oEAAE,OAAO;oEAAK,MAAM;gEAAS;0EAEzC,cAAA,8OAAC;oEAAK,WAAU;8EAAuB;;;;;;;;;;;0EAEzC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAIvC,8OAAC,OAAO,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,SAAS;wDAAE;wDACtB,SAAS;4DAAE,SAAS;wDAAE;wDACtB,YAAY;4DAAE,OAAO;wDAAI;;0EAEzB,8OAAC,OAAO,GAAG;gEACT,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAM;gEACxB,YAAY;oEAAE,OAAO;oEAAK,UAAU;gEAAI;;;;;;0EAE1C,8OAAC,OAAO,GAAG;gEACT,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAM;gEACxB,YAAY;oEAAE,OAAO;oEAAG,UAAU;gEAAI;;;;;;0EAExC,8OAAC,OAAO,GAAG;gEACT,WAAU;gEACV,SAAS;oEAAE,QAAQ;gEAAE;gEACrB,SAAS;oEAAE,QAAQ;gEAAO;gEAC1B,YAAY;oEAAE,OAAO;oEAAK,UAAU;gEAAI;;;;;;0EAE1C,8OAAC,OAAO,GAAG;gEACT,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAa;gEAC/B,YAAY;oEAAE,OAAO;oEAAK,UAAU;gEAAI;;;;;;;;;;;;kEAK5C,8OAAC,OAAO,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC7B,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAC5B,YAAY;4DAAE,OAAO;wDAAI;kEAExB;4DAAC;4DAAG;4DAAG;4DAAG;yDAAE,CAAC,GAAG,CAAC,CAAC,sBACjB,8OAAC,OAAO,GAAG;gEAET,WAAW,CAAC,gBAAgB,EAAE,UAAU,IAAI,eAAe,eAAe;gEAC1E,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAE;gEACpB,YAAY;oEAAE,OAAO,MAAM,QAAQ;oEAAK,MAAM;gEAAS;+DAJlD;;;;;;;;;;;+CAnEP;;;;uDA6EN,gBAAgB;0DAChB,8OAAC,OAAO,GAAG;gDAET,WAAU;gDACV,SAAS;oDAAE,SAAS;gDAAE;gDACtB,SAAS;oDAAE,SAAS;gDAAE;;kEAEtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAIvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;kEAIjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;+CAxBb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqC5B", "debugId": null}}, {"offset": {"line": 1362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/StaggeredList.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface StaggeredListProps {\n  children: React.ReactNode[];\n  className?: string;\n  staggerDelay?: number;\n}\n\nexport default function StaggeredList({ \n  children, \n  className = '',\n  staggerDelay = 0.1\n}: StaggeredListProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: staggerDelay,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { \n      y: 30, \n      opacity: 0 \n    },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5,\n        ease: [0.25, 0.25, 0.25, 0.75]\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      className={className}\n      variants={containerVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: \"-50px\" }}\n    >\n      {children.map((child, index) => (\n        <motion.div key={index} variants={itemVariants}>\n          {child}\n        </motion.div>\n      ))}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAWe,SAAS,cAAc,EACpC,QAAQ,EACR,YAAY,EAAE,EACd,eAAe,GAAG,EACC;IACnB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,GAAG;YACH,SAAS;QACX;QACA,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAM;oBAAM;iBAAK;YAChC;QACF;IACF;IAEA,qBACE,8OAAC,OAAO,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAQ;kBAEvC,SAAS,GAAG,CAAC,CAAC,OAAO,sBACpB,8OAAC,OAAO,GAAG;gBAAa,UAAU;0BAC/B;eADc;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 1435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport AnimatedSection from './AnimatedSection';\n\nexport default function Contact() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    project: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n\n    try {\n      // Web3Forms integration\n      const response = await fetch('https://api.web3forms.com/submit', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          access_key: 'YOUR_WEB3FORMS_ACCESS_KEY', // This will need to be replaced with actual key\n          name: formData.name,\n          email: formData.email,\n          message: formData.project,\n          subject: 'New Mobilify Contact Form Submission',\n          from_name: 'Mobilify Website',\n          to_email: '<EMAIL>'\n        }),\n      });\n\n      if (response.ok) {\n        setSubmitStatus('success');\n        setFormData({ name: '', email: '', project: '' });\n      } else {\n        setSubmitStatus('error');\n      }\n    } catch (error) {\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Section Header */}\n          <AnimatedSection className=\"text-center mb-16\">\n            <h2 className=\"text-4xl sm:text-5xl font-bold text-foreground mb-6\">\n              Ready to Build Your <span className=\"text-primary\">Mobile Future?</span>\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Let's discuss your project. We're happy to provide a free,\n              no-obligation consultation and quote.\n            </p>\n          </AnimatedSection>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n            {/* Contact Form */}\n            <AnimatedSection className=\"bg-gray-50 rounded-2xl p-8\" direction=\"left\">\n              <h3 className=\"text-2xl font-bold text-foreground mb-6\">Get Started Today</h3>\n              \n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                {/* Name Field */}\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Your Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleChange}\n                    required\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200\"\n                    placeholder=\"John Doe\"\n                  />\n                </div>\n\n                {/* Email Field */}\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Email Address *\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    required\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                </div>\n\n                {/* Project Description */}\n                <div>\n                  <label htmlFor=\"project\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Briefly describe your project *\n                  </label>\n                  <textarea\n                    id=\"project\"\n                    name=\"project\"\n                    value={formData.project}\n                    onChange={handleChange}\n                    required\n                    rows={4}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 resize-none\"\n                    placeholder=\"Tell us about your app idea, target audience, key features, or website you'd like to convert...\"\n                  />\n                </div>\n\n                {/* Submit Button */}\n                <button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className=\"w-full bg-primary text-white py-4 rounded-lg font-semibold text-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105\"\n                >\n                  {isSubmitting ? (\n                    <span className=\"flex items-center justify-center\">\n                      <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                      Sending...\n                    </span>\n                  ) : (\n                    'Send Message'\n                  )}\n                </button>\n\n                {/* Status Messages */}\n                {submitStatus === 'success' && (\n                  <div className=\"p-4 bg-green-50 border border-green-200 rounded-lg\">\n                    <p className=\"text-green-800 text-sm\">\n                      ✅ Thank you! Your message has been sent successfully. We'll get back to you within 24 hours.\n                    </p>\n                  </div>\n                )}\n\n                {submitStatus === 'error' && (\n                  <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\n                    <p className=\"text-red-800 text-sm\">\n                      ❌ Sorry, there was an error sending your message. Please try again or contact us directly.\n                    </p>\n                  </div>\n                )}\n              </form>\n            </AnimatedSection>\n\n            {/* Contact Information */}\n            <AnimatedSection className=\"space-y-8\" direction=\"right\" delay={0.2}>\n              <div>\n                <h3 className=\"text-2xl font-bold text-foreground mb-6\">Let's Connect</h3>\n                <p className=\"text-gray-600 mb-8\">\n                  Have questions? Want to discuss your project in detail? \n                  We're here to help you turn your vision into reality.\n                </p>\n              </div>\n\n              {/* Contact Methods */}\n              <div className=\"space-y-6\">\n                <div className=\"flex items-start\">\n                  <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                    <svg className=\"w-6 h-6 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-foreground mb-1\">Email Us</h4>\n                    <p className=\"text-gray-600\"><EMAIL></p>\n                    <p className=\"text-sm text-gray-500\">We respond within 24 hours</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                    <svg className=\"w-6 h-6 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-foreground mb-1\">Response Time</h4>\n                    <p className=\"text-gray-600\">Within 24 hours</p>\n                    <p className=\"text-sm text-gray-500\">Usually much faster!</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                    <svg className=\"w-6 h-6 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-foreground mb-1\">Free Consultation</h4>\n                    <p className=\"text-gray-600\">No obligation quote</p>\n                    <p className=\"text-sm text-gray-500\">Let's discuss your project</p>\n                  </div>\n                </div>\n              </div>\n            </AnimatedSection>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAE/E,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,wBAAwB;YACxB,MAAM,WAAW,MAAM,MAAM,oCAAoC;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY;oBACZ,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,SAAS,SAAS,OAAO;oBACzB,SAAS;oBACT,WAAW;oBACX,UAAU;gBACZ;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,gBAAgB;gBAChB,YAAY;oBAAE,MAAM;oBAAI,OAAO;oBAAI,SAAS;gBAAG;YACjD,OAAO;gBACL,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;QAClB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,qIAAA,CAAA,UAAe;wBAAC,WAAU;;0CACzB,8OAAC;gCAAG,WAAU;;oCAAsD;kDAC9C,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAErD,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,qIAAA,CAAA,UAAe;gCAAC,WAAU;gCAA6B,WAAU;;kDAChE,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDAExD,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DAEtC,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAO,WAAU;kEAA+C;;;;;;kEAG/E,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU;wDACV,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAKhB,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAA+C;;;;;;kEAGhF,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAKhB,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAA+C;;;;;;kEAGlF,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,QAAQ;wDACR,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAKhB,8OAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,6BACC,8OAAC;oDAAK,WAAU;;sEACd,8OAAC;4DAAI,WAAU;4DAA6C,OAAM;4DAA6B,MAAK;4DAAO,SAAQ;;8EACjH,8OAAC;oEAAO,WAAU;oEAAa,IAAG;oEAAK,IAAG;oEAAK,GAAE;oEAAK,QAAO;oEAAe,aAAY;;;;;;8EACxF,8OAAC;oEAAK,WAAU;oEAAa,MAAK;oEAAe,GAAE;;;;;;;;;;;;wDAC/C;;;;;;2DAIR;;;;;;4CAKH,iBAAiB,2BAChB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;4CAMzC,iBAAiB,yBAChB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAuB;;;;;;;;;;;;;;;;;;;;;;;0CAS5C,8OAAC,qIAAA,CAAA,UAAe;gCAAC,WAAU;gCAAY,WAAU;gCAAQ,OAAO;;kDAC9D,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA0C;;;;;;0DACxD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAOpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqC;;;;;;0EACnD,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqC;;;;;;0EACnD,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqC;;;;;;0EACnD,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD", "debugId": null}}]}