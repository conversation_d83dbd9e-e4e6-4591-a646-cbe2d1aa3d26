{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/InteractiveDemo.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/InteractiveDemo.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/InteractiveDemo.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/InteractiveDemo.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/InteractiveDemo.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/InteractiveDemo.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/AnimatedSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AnimatedSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AnimatedSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/AnimatedSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AnimatedSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AnimatedSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/StaggeredList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StaggeredList.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StaggeredList.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/StaggeredList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StaggeredList.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StaggeredList.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ServicesOverview.tsx"], "sourcesContent": ["import React from 'react';\nimport AnimatedSection from './AnimatedSection';\nimport StaggeredList from './StaggeredList';\n\nconst services = [\n  {\n    title: 'Starter App',\n    price: '$5,000',\n    description: 'Perfect for converting existing websites into mobile apps',\n    features: [\n      'Website to app conversion',\n      'Basic customization',\n      'App store submission',\n      '30 days support'\n    ],\n    popular: false\n  },\n  {\n    title: 'Custom App',\n    price: '$15,000',\n    description: 'Turn your innovative ideas into reality with custom development',\n    features: [\n      'Custom app development',\n      'Advanced features',\n      'UI/UX design included',\n      'App store optimization',\n      '90 days support'\n    ],\n    popular: true\n  },\n  {\n    title: 'Enterprise',\n    price: 'Contact Us',\n    description: 'Bespoke solutions for complex projects and large organizations',\n    features: [\n      'Complex integrations',\n      'Scalable architecture',\n      'Dedicated team',\n      'Ongoing maintenance',\n      'Priority support'\n    ],\n    popular: false\n  }\n];\n\nexport default function ServicesOverview() {\n  return (\n    <section id=\"services-overview\" className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <AnimatedSection className=\"text-center mb-16\">\n          <h2 className=\"text-4xl sm:text-5xl font-bold text-foreground mb-6\">\n            Solutions Tailored to <span className=\"text-primary\">Your Needs</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Whether you're converting an existing website or building from scratch,\n            we have the perfect package for your project.\n          </p>\n        </AnimatedSection>\n\n        {/* Service Cards */}\n        <StaggeredList className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n          {services.map((service, index) => (\n            <div\n              key={index}\n              className={`relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:scale-105 group cursor-pointer ${\n                service.popular ? 'ring-2 ring-primary scale-105' : ''\n              }`}\n            >\n              {service.popular && (\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                  <span className=\"bg-primary text-white px-4 py-2 rounded-full text-sm font-semibold\">\n                    Most Popular\n                  </span>\n                </div>\n              )}\n              \n              <div className=\"text-center mb-6\">\n                <h3 className=\"text-2xl font-bold text-foreground mb-2\">{service.title}</h3>\n                <div className=\"text-3xl font-bold text-primary mb-4\">{service.price}</div>\n                <p className=\"text-gray-600\">{service.description}</p>\n              </div>\n\n              <ul className=\"space-y-3 mb-8\">\n                {service.features.map((feature, featureIndex) => (\n                  <li key={featureIndex} className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-primary mr-3 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span className=\"text-gray-700\">{feature}</span>\n                  </li>\n                ))}\n              </ul>\n\n              <button className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 transform group-hover:scale-105 ${\n                service.popular\n                  ? 'bg-primary text-white hover:bg-primary/90 shadow-lg hover:shadow-xl'\n                  : 'bg-gray-100 text-gray-800 hover:bg-primary hover:text-white shadow-md hover:shadow-lg'\n              }`}>\n                Get Started\n              </button>\n            </div>\n          ))}\n        </StaggeredList>\n\n        {/* Deep Dive Link */}\n        <AnimatedSection className=\"text-center\" delay={0.6}>\n          <button className=\"inline-flex items-center bg-white text-primary px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-50 transition-all duration-200 shadow-md hover:shadow-lg\">\n            Compare All Features & Pricing\n            <svg className=\"ml-2 w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n            </svg>\n          </button>\n        </AnimatedSection>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,WAAW;IACf;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAoB,WAAU;kBACxC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,qIAAA,CAAA,UAAe;oBAAC,WAAU;;sCACzB,8OAAC;4BAAG,WAAU;;gCAAsD;8CAC5C,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAEvD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC,mIAAA,CAAA,UAAa;oBAAC,WAAU;8BACtB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAEC,WAAW,CAAC,6JAA6J,EACvK,QAAQ,OAAO,GAAG,kCAAkC,IACpD;;gCAED,QAAQ,OAAO,kBACd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAqE;;;;;;;;;;;8CAMzF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C,QAAQ,KAAK;;;;;;sDACtE,8OAAC;4CAAI,WAAU;sDAAwC,QAAQ,KAAK;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAAiB,QAAQ,WAAW;;;;;;;;;;;;8CAGnD,8OAAC;oCAAG,WAAU;8CACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;4CAAsB,WAAU;;8DAC/B,8OAAC;oDAAI,WAAU;oDAA0C,MAAK;oDAAe,SAAQ;8DACnF,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;8DAE3J,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;;2CAJ1B;;;;;;;;;;8CASb,8OAAC;oCAAO,WAAW,CAAC,iGAAiG,EACnH,QAAQ,OAAO,GACX,wEACA,yFACJ;8CAAE;;;;;;;2BAlCC;;;;;;;;;;8BA0CX,8OAAC,qIAAA,CAAA,UAAe;oBAAC,WAAU;oBAAc,OAAO;8BAC9C,cAAA,8OAAC;wBAAO,WAAU;;4BAAmK;0CAEnL,8OAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACtE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF", "debugId": null}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Process.tsx"], "sourcesContent": ["import React from 'react';\nimport AnimatedSection from './AnimatedSection';\nimport StaggeredList from './StaggeredList';\n\nconst steps = [\n  {\n    number: '01',\n    title: 'Discovery & Strategy',\n    description: 'We dive deep into your vision and goals, understanding your target audience and business objectives to create the perfect roadmap.',\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n      </svg>\n    )\n  },\n  {\n    number: '02',\n    title: 'Design & Development',\n    description: 'Our expert team builds your app with precision and care, focusing on user experience, performance, and scalability.',\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\" />\n      </svg>\n    )\n  },\n  {\n    number: '03',\n    title: 'Launch & Support',\n    description: 'We handle app store submission, ensure smooth deployment, and provide ongoing support to keep your app running perfectly.',\n    icon: (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n      </svg>\n    )\n  }\n];\n\nexport default function Process() {\n  return (\n    <section id=\"process\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <AnimatedSection className=\"text-center mb-16\">\n          <h2 className=\"text-4xl sm:text-5xl font-bold text-foreground mb-6\">\n            Your Clear Path to <span className=\"text-primary\">Launch</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Our proven process ensures your app is built efficiently,\n            meets your requirements, and launches successfully.\n          </p>\n        </AnimatedSection>\n\n        {/* Process Steps */}\n        <div className=\"relative\">\n          {/* Desktop Layout */}\n          <div className=\"hidden lg:block\">\n            <StaggeredList className=\"flex items-center justify-between\">\n              {steps.map((step, index) => (\n                <div key={index} className=\"flex-1 relative group\">\n                  {/* Step Content */}\n                  <div className=\"text-center transition-all duration-300 group-hover:transform group-hover:-translate-y-2\">\n                    {/* Icon */}\n                    <div className=\"w-20 h-20 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg transition-all duration-300 group-hover:shadow-2xl group-hover:scale-110 group-hover:bg-primary/90\">\n                      {step.icon}\n                    </div>\n                    \n                    {/* Step Number */}\n                    <div className=\"text-6xl font-bold text-gray-200 mb-4\">{step.number}</div>\n                    \n                    {/* Title */}\n                    <h3 className=\"text-2xl font-bold text-foreground mb-4\">{step.title}</h3>\n                    \n                    {/* Description */}\n                    <p className=\"text-gray-600 max-w-sm mx-auto\">{step.description}</p>\n                  </div>\n\n                  {/* Connector Line */}\n                  {index < steps.length - 1 && (\n                    <div className=\"absolute top-10 left-1/2 w-full h-0.5 bg-gray-300 transform translate-x-1/2 z-0\">\n                      <div className=\"absolute right-0 top-1/2 transform -translate-y-1/2\">\n                        <svg className=\"w-4 h-4 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                        </svg>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </StaggeredList>\n          </div>\n\n          {/* Mobile Layout */}\n          <StaggeredList className=\"lg:hidden space-y-12\">\n            {steps.map((step, index) => (\n              <div key={index} className=\"relative\">\n                <div className=\"flex items-start\">\n                  {/* Icon and Number */}\n                  <div className=\"flex-shrink-0 mr-6\">\n                    <div className=\"w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center shadow-lg\">\n                      {step.icon}\n                    </div>\n                    <div className=\"text-4xl font-bold text-gray-200 mt-2 text-center\">{step.number}</div>\n                  </div>\n                  \n                  {/* Content */}\n                  <div className=\"flex-1 pt-2\">\n                    <h3 className=\"text-xl font-bold text-foreground mb-3\">{step.title}</h3>\n                    <p className=\"text-gray-600\">{step.description}</p>\n                  </div>\n                </div>\n\n                {/* Connector Line */}\n                {index < steps.length - 1 && (\n                  <div className=\"absolute left-8 top-20 w-0.5 h-12 bg-gray-300\"></div>\n                )}\n              </div>\n            ))}\n          </StaggeredList>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,QAAQ;IACZ;QACE,QAAQ;QACR,OAAO;QACP,aAAa;QACb,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,QAAQ;QACR,OAAO;QACP,aAAa;QACb,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,QAAQ;QACR,OAAO;QACP,aAAa;QACb,oBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,qIAAA,CAAA,UAAe;oBAAC,WAAU;;sCACzB,8OAAC;4BAAG,WAAU;;gCAAsD;8CAC/C,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAEpD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,mIAAA,CAAA,UAAa;gCAAC,WAAU;0CACtB,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;wCAAgB,WAAU;;0DAEzB,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI;;;;;;kEAIZ,8OAAC;wDAAI,WAAU;kEAAyC,KAAK,MAAM;;;;;;kEAGnE,8OAAC;wDAAG,WAAU;kEAA2C,KAAK,KAAK;;;;;;kEAGnE,8OAAC;wDAAE,WAAU;kEAAkC,KAAK,WAAW;;;;;;;;;;;;4CAIhE,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAA2I,UAAS;;;;;;;;;;;;;;;;;;;;;;uCAvB/K;;;;;;;;;;;;;;;sCAkChB,8OAAC,mIAAA,CAAA,UAAa;4BAAC,WAAU;sCACtB,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,KAAK,IAAI;;;;;;sEAEZ,8OAAC;4DAAI,WAAU;sEAAqD,KAAK,MAAM;;;;;;;;;;;;8DAIjF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA0C,KAAK,KAAK;;;;;;sEAClE,8OAAC;4DAAE,WAAU;sEAAiB,KAAK,WAAW;;;;;;;;;;;;;;;;;;wCAKjD,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;4CAAI,WAAU;;;;;;;mCAnBT;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BxB", "debugId": null}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/About.tsx"], "sourcesContent": ["import React from 'react';\nimport AnimatedSection from './AnimatedSection';\nimport StaggeredList from './StaggeredList';\n\nexport default function About() {\n  return (\n    <section id=\"about\" className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          {/* Section Header */}\n          <AnimatedSection>\n            <h2 className=\"text-4xl sm:text-5xl font-bold text-foreground mb-6\">\n              We're More Than Just <span className=\"text-primary\">Developers</span>\n            </h2>\n          </AnimatedSection>\n          \n          {/* Mission Statement */}\n          <AnimatedSection className=\"bg-white rounded-2xl p-8 md:p-12 shadow-lg mb-12\" delay={0.2}>\n            <p className=\"text-xl text-gray-700 leading-relaxed mb-8\">\n              At Mobilify, we believe that every great idea deserves to become a reality. \n              We're passionate about helping founders, entrepreneurs, and businesses bridge \n              the gap between vision and execution.\n            </p>\n            \n            <p className=\"text-lg text-gray-600 leading-relaxed mb-8\">\n              Our commitment goes beyond just writing code. We partner with you to understand \n              your goals, challenges, and dreams. We're not satisfied until your app not only \n              works flawlessly but also delights your users and drives your business forward.\n            </p>\n            \n            <StaggeredList className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8\">\n              {/* Value 1 */}\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-semibold text-foreground mb-2\">Quality Craftsmanship</h3>\n                <p className=\"text-gray-600 text-sm\">Every line of code is written with precision and care</p>\n              </div>\n              \n              {/* Value 2 */}\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-semibold text-foreground mb-2\">Client Partnership</h3>\n                <p className=\"text-gray-600 text-sm\">We work alongside you as true partners in success</p>\n              </div>\n              \n              {/* Value 3 */}\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-semibold text-foreground mb-2\">Transparency</h3>\n                <p className=\"text-gray-600 text-sm\">Clear communication and honest timelines, always</p>\n              </div>\n            </StaggeredList>\n          </AnimatedSection>\n          \n          {/* Team Link */}\n          <AnimatedSection className=\"text-center\" delay={0.4}>\n            <p className=\"text-lg text-gray-600 mb-6\">\n              Ready to meet the passionate team behind Mobilify?\n            </p>\n            <button className=\"inline-flex items-center text-primary font-semibold text-lg hover:text-primary/80 transition-colors duration-200 group\">\n              Meet the Team\n              <svg className=\"ml-2 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n              </svg>\n            </button>\n          </AnimatedSection>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,qIAAA,CAAA,UAAe;kCACd,cAAA,8OAAC;4BAAG,WAAU;;gCAAsD;8CAC7C,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;;;;;;kCAKxD,8OAAC,qIAAA,CAAA,UAAe;wBAAC,WAAU;wBAAmD,OAAO;;0CACnF,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAM1D,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAM1D,8OAAC,mIAAA,CAAA,UAAa;gCAAC,WAAU;;kDAEvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAuB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC9E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAC3D,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAIvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAuB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC9E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAC3D,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAIvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAuB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;;sEAC9E,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;sEACrE,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAC3D,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAM3C,8OAAC,qIAAA,CAAA,UAAe;wBAAC,WAAU;wBAAc,OAAO;;0CAC9C,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAO,WAAU;;oCAAyH;kDAEzI,8OAAC;wCAAI,WAAU;wCAAqF,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5I,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Contact.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Contact.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Contact.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 1138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Contact.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Contact.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Contact.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,wCACA", "debugId": null}}, {"offset": {"line": 1152, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Logo.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LogoProps {\n  className?: string;\n}\n\nexport default function Logo({ className = \"\" }: LogoProps) {\n  return (\n    <div className={`w-10 h-10 bg-logo-bg rounded-lg flex items-center justify-center ${className}`}>\n      <span className=\"text-white font-bold text-xl\">M</span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAMe,SAAS,KAAK,EAAE,YAAY,EAAE,EAAa;IACxD,qBACE,8OAAC;QAAI,WAAW,CAAC,iEAAiE,EAAE,WAAW;kBAC7F,cAAA,8OAAC;YAAK,WAAU;sBAA+B;;;;;;;;;;;AAGrD", "debugId": null}}, {"offset": {"line": 1190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Logo from './Logo';\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-gray-900 text-white py-12\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"md:col-span-2\">\n            <div className=\"flex items-center mb-4\">\n              <Logo />\n              <span className=\"ml-3 text-xl font-bold\">Mobilify</span>\n            </div>\n            <p className=\"text-gray-300 mb-4 max-w-md\">\n              Transforming your concepts and existing websites into stunning, \n              high-performance mobile apps. We are the bridge from vision to launch.\n            </p>\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                <span className=\"sr-only\">Twitter</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                <span className=\"sr-only\">LinkedIn</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                <span className=\"sr-only\">GitHub</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <a href=\"/services\" className=\"text-gray-300 hover:text-white transition-colors duration-200\">\n                  Services\n                </a>\n              </li>\n              <li>\n                <a href=\"/#process\" className=\"text-gray-300 hover:text-white transition-colors duration-200\">\n                  How It Works\n                </a>\n              </li>\n              <li>\n                <a href=\"/about\" className=\"text-gray-300 hover:text-white transition-colors duration-200\">\n                  About Us\n                </a>\n              </li>\n              <li>\n                <a href=\"/#contact\" className=\"text-gray-300 hover:text-white transition-colors duration-200\">\n                  Contact\n                </a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Contact</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><EMAIL></li>\n              <li>Response within 24 hours</li>\n              <li>Free consultation</li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-gray-400 text-sm\">\n            © {currentYear} Mobilify. All rights reserved.\n          </p>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n              Privacy Policy\n            </a>\n            <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n              Terms of Service\n            </a>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAEe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0HAAA,CAAA,UAAI;;;;;sDACL,8OAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAgE;;;;;;;;;;;sDAIhG,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAgE;;;;;;;;;;;sDAIhG,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAS,WAAU;0DAAgE;;;;;;;;;;;sDAI7F,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAgE;;;;;;;;;;;;;;;;;;;;;;;sCAQpG,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;8BAMV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAAwB;gCAChC;gCAAY;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAwE;;;;;;8CAG9F,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1G", "debugId": null}}, {"offset": {"line": 1560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/app/page.tsx"], "sourcesContent": ["import Header from '@/components/Header';\nimport Hero from '@/components/Hero';\nimport InteractiveDemo from '@/components/InteractiveDemo';\nimport ServicesOverview from '@/components/ServicesOverview';\nimport Process from '@/components/Process';\nimport About from '@/components/About';\nimport Contact from '@/components/Contact';\nimport Footer from '@/components/Footer';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header />\n\n      <main>\n        <Hero />\n        <InteractiveDemo />\n        <ServicesOverview />\n        <Process />\n        <About />\n        <Contact />\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;;kCACC,8OAAC,0HAAA,CAAA,UAAI;;;;;kCACL,8OAAC,qIAAA,CAAA,UAAe;;;;;kCAChB,8OAAC,sIAAA,CAAA,UAAgB;;;;;kCACjB,8OAAC,6HAAA,CAAA,UAAO;;;;;kCACR,8OAAC,2HAAA,CAAA,UAAK;;;;;kCACN,8OAAC,6HAAA,CAAA,UAAO;;;;;;;;;;;0BAGV,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}